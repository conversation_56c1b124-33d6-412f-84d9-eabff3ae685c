#!/usr/bin/env python3
"""
Image Toolbox Launcher
This script ensures the application runs with the correct virtual environment
and can be used with Automator or run directly.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

def setup_logging():
    """Set up logging to file"""
    script_dir = Path(__file__).parent
    log_file = script_dir / "launcher.log"
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file),
            logging.StreamHandler()
        ]
    )
    return logging.getLogger(__name__)

def main():
    logger = setup_logging()
    logger.info("=== Starting Image Toolbox Application ===")
    
    # Get the directory where this script is located
    script_dir = Path(__file__).parent.resolve()
    logger.info(f"Script directory: {script_dir}")
    
    # Paths
    venv_path = script_dir / "venv"
    main_app = script_dir / "main_app.py"
    python_executable = venv_path / "bin" / "python"
    
    logger.info(f"Virtual environment: {venv_path}")
    logger.info(f"Main application: {main_app}")
    logger.info(f"Python executable: {python_executable}")
    
    # Check if virtual environment exists
    if not venv_path.exists():
        error_msg = f"Virtual environment not found at {venv_path}"
        logger.error(error_msg)
        print(f"ERROR: {error_msg}")
        return 1
    
    # Check if Python executable exists
    if not python_executable.exists():
        error_msg = f"Python executable not found at {python_executable}"
        logger.error(error_msg)
        print(f"ERROR: {error_msg}")
        return 1
    
    # Check if main application exists
    if not main_app.exists():
        error_msg = f"Main application not found at {main_app}"
        logger.error(error_msg)
        print(f"ERROR: {error_msg}")
        return 1
    
    # Change to project directory
    os.chdir(script_dir)
    logger.info(f"Changed to project directory: {os.getcwd()}")
    
    # Set up environment
    env = os.environ.copy()
    env['PYTHONPATH'] = str(script_dir)
    
    logger.info("Starting main application...")
    
    try:
        # Run the application using the virtual environment's Python
        result = subprocess.run(
            [str(python_executable), str(main_app)],
            env=env,
            cwd=script_dir
        )
        
        logger.info(f"Application finished with return code: {result.returncode}")
        return result.returncode
        
    except Exception as e:
        logger.error(f"Error running application: {e}")
        print(f"ERROR: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
