<!DOCTYPE html>
<html lang="en" class="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Toolbox Pro</title>
    
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Lucide Icons for a modern icon set -->
    <script src="https://unpkg.com/lucide-react@0.395.0/dist/lucide-react.js"></script>
    <script src="https://unpkg.com/lucide@latest"></script>

    <style>
        /* Custom scrollbar for a cleaner look */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }
        ::-webkit-scrollbar-track {
            background: transparent;
        }
        ::-webkit-scrollbar-thumb {
            background-color: #4a5568; /* gray-600 */
            border-radius: 4px;
        }
        .dark ::-webkit-scrollbar-thumb {
            background-color: #a0aec0; /* gray-400 */
        }
        ::-webkit-scrollbar-thumb:hover {
            background-color: #2d3748; /* gray-800 */
        }
        .dark ::-webkit-scrollbar-thumb:hover {
            background-color: #cbd5e0; /* gray-300 */
        }

        /* Custom styles for resizers */
        .resizer-y {
            cursor: ns-resize;
            height: 6px;
            width: 100%;
        }
        .resizer-x {
            cursor: ew-resize;
            width: 6px;
            height: 100%;
        }
        
        /* Hide all tool workspaces by default */
        .tool-workspace {
            display: none;
        }
        /* Show the active tool workspace */
        .tool-workspace.active {
            display: flex;
        }
        
        /* Hide all log panels by default */
        .log-panel {
            display: none;
        }
        /* Show the active log panel */
        .log-panel.active {
            display: flex;
        }
    </style>
</head>
<body class="bg-gray-100 dark:bg-gray-900 text-gray-800 dark:text-gray-200 font-sans antialiased">

    <div id="app-container" class="flex flex-col h-screen overflow-hidden">
        
        <!-- ======================= -->
        <!--      HEADER BAR         -->
        <!-- ======================= -->
        <header class="flex items-center justify-between px-4 py-2 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm flex-shrink-0">
            <div class="flex items-center space-x-3">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-blue-500"><path d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z"></path><polyline points="3.27 6.96 12 12.01 20.73 6.96"></polyline><line x1="12" y1="22.08" x2="12" y2="12"></line></svg>
                <h1 class="text-lg font-bold">Image Toolbox Pro</h1>
            </div>
            <div class="flex items-center space-x-4">
                <button id="theme-toggle" class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <i data-lucide="sun" class="block dark:hidden"></i>
                    <i data-lucide="moon" class="hidden dark:block"></i>
                </button>
                <button class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <i data-lucide="settings"></i>
                </button>
                <button class="p-2 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <i data-lucide="help-circle"></i>
                </button>
            </div>
        </header>

        <div class="flex flex-1 overflow-hidden">
            <!-- ======================= -->
            <!--   NAVIGATION RAIL       -->
            <!-- ======================= -->
            <nav id="nav-rail" class="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 p-2 flex flex-col space-y-1 overflow-y-auto">
                <!-- Navigation items will be injected by JS -->
            </nav>
            
            <div class="resizer-x bg-gray-200 dark:bg-gray-700 hover:bg-blue-500 transition-colors" id="nav-resizer"></div>

            <main class="flex-1 flex flex-col overflow-hidden">
                <!-- ======================= -->
                <!--    TOOL WORKSPACE       -->
                <!-- ======================= -->
                <div id="tool-workspace-container" class="flex-1 p-4 md:p-6 overflow-y-auto flex flex-col">
                    
                    <!-- Placeholder for no tool selected -->
                    <div id="tool-placeholder" class="tool-workspace active flex-col items-center justify-center text-center text-gray-500">
                        <i data-lucide="layout-dashboard" class="w-16 h-16 mb-4"></i>
                        <h2 class="text-2xl font-semibold">Welcome to Image Toolbox Pro</h2>
                        <p>Select a tool from the left navigation panel to get started.</p>
                    </div>

                    <!-- Clip Combiner Workspace -->
                    <div id="tool-clip-combiner" class="tool-workspace flex-col space-y-6">
                        <h2 class="text-2xl font-bold flex items-center space-x-2"><i data-lucide="combine"></i><span>Clip Image Combiner</span></h2>
                        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium mb-1">Input Directory</label>
                                    <div class="flex">
                                        <input type="text" placeholder="/path/to/images" class="flex-grow bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-l-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                                        <button class="bg-blue-500 text-white px-4 py-2 rounded-r-md hover:bg-blue-600 flex items-center"><i data-lucide="folder-open" class="w-4 h-4 mr-2"></i>Browse</button>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-1">Output Directory</label>
                                    <div class="flex">
                                        <input type="text" placeholder="/path/to/output" class="flex-grow bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-l-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                                        <button class="bg-blue-500 text-white px-4 py-2 rounded-r-md hover:bg-blue-600 flex items-center"><i data-lucide="folder-output" class="w-4 h-4 mr-2"></i>Browse</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700">
                            <h3 class="text-lg font-semibold mb-4 flex items-center"><i data-lucide="sliders-horizontal" class="mr-2"></i>Settings</h3>
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                <div>
                                    <label class="block text-sm font-medium mb-1">Layout Mode</label>
                                    <select class="w-full bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2">
                                        <option>Grid</option><option>Horizontal</option><option>Vertical</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-1">Max Batch Size</label>
                                    <input type="number" value="4" class="w-full bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium mb-1">CLIP Model</label>
                                    <select class="w-full bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2">
                                        <option>ViT-B/32</option><option>ViT-L/14</option>
                                    </select>
                                </div>
                                <div class="md:col-span-2">
                                    <label class="block text-sm font-medium mb-1">CLIP Similarity Threshold: <span class="font-bold text-blue-500">0.85</span></label>
                                    <input type="range" min="0.5" max="0.95" value="0.85" step="0.01" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700">
                                </div>
                                <div class="flex items-center space-x-2 pt-5">
                                    <input id="master-file" type="checkbox" class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                                    <label for="master-file" class="text-sm font-medium">Generate Master File</label>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button class="bg-blue-500 text-white px-6 py-2 rounded-md hover:bg-blue-600 flex items-center font-semibold"><i data-lucide="download-cloud" class="w-5 h-5 mr-2"></i>Load Model</button>
                            <button class="bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600 flex items-center font-semibold"><i data-lucide="play" class="w-5 h-5 mr-2"></i>Process Images</button>
                            <button class="bg-gray-500 text-white px-6 py-2 rounded-md hover:bg-gray-600 flex items-center font-semibold"><i data-lucide="rotate-ccw" class="w-5 h-5 mr-2"></i>Reset</button>
                        </div>
                    </div>

                    <!-- Upscale Remover Workspace -->
                    <div id="tool-upscale-remover" class="tool-workspace flex-col space-y-6">
                        <h2 class="text-2xl font-bold flex items-center space-x-2"><i data-lucide="trash-2"></i><span>Upscale Remover</span></h2>
                        <div class="bg-yellow-100 dark:bg-yellow-900/50 border-l-4 border-yellow-500 text-yellow-800 dark:text-yellow-200 p-4 rounded-md" role="alert">
                            <p class="font-bold">Warning</p>
                            <p>This tool permanently deletes files. Duplicates will be removed from Folder One. This action cannot be undone.</p>
                        </div>
                        <div class="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 space-y-4">
                             <div>
                                <label class="block text-sm font-medium mb-1">Folder One (Source to delete from)</label>
                                <div class="flex">
                                    <input type="text" placeholder="/path/to/source_folder" class="flex-grow bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-l-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                                    <button class="bg-blue-500 text-white px-4 py-2 rounded-r-md hover:bg-blue-600 flex items-center"><i data-lucide="folder-open" class="w-4 h-4 mr-2"></i>Browse</button>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium mb-1">Folder Two (Reference folder)</label>
                                <div class="flex">
                                    <input type="text" placeholder="/path/to/reference_folder" class="flex-grow bg-gray-100 dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-l-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500">
                                    <button class="bg-blue-500 text-white px-4 py-2 rounded-r-md hover:bg-blue-600 flex items-center"><i data-lucide="folder-check" class="w-4 h-4 mr-2"></i>Browse</button>
                                </div>
                            </div>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button class="bg-red-500 text-white px-6 py-2 rounded-md hover:bg-red-600 flex items-center font-semibold"><i data-lucide="trash-2" class="w-5 h-5 mr-2"></i>Remove Duplicates</button>
                        </div>
                    </div>
                    
                    <!-- Other tool workspaces would go here -->
                     <div id="tool-csv-converter" class="tool-workspace flex-col items-center justify-center text-gray-500">
                        <i data-lucide="file-text" class="w-16 h-16 mb-4"></i><h2 class="text-2xl font-semibold">CSV Converter</h2><p>UI for this tool goes here.</p>
                    </div>
                     <div id="tool-image-mover" class="tool-workspace flex-col items-center justify-center text-gray-500">
                        <i data-lucide="move-right" class="w-16 h-16 mb-4"></i><h2 class="text-2xl font-semibold">Image Mover</h2><p>UI for this tool goes here.</p>
                    </div>
                </div>

                <!-- ======================= -->
                <!--    LOG CONSOLE RESIZER  -->
                <!-- ======================= -->
                <div class="resizer-y bg-gray-200 dark:bg-gray-700 hover:bg-blue-500 transition-colors" id="log-resizer"></div>
                
                <!-- ======================= -->
                <!--    LOG CONSOLE          -->
                <!-- ======================= -->
                <div id="log-console" class="h-64 flex-shrink-0 bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 flex flex-col overflow-hidden">
                    <!-- Tabs and Controls -->
                    <div class="flex items-center justify-between px-2 border-b border-gray-200 dark:border-gray-700 flex-shrink-0">
                        <div class="flex items-center space-x-1" id="log-tabs">
                            <button data-tab="log-console-panel" class="log-tab-btn px-4 py-2 text-sm font-medium border-b-2 border-blue-500 text-blue-500">Console Log</button>
                            <button data-tab="task-queue-panel" class="log-tab-btn px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">Task Queue <span class="bg-blue-100 text-blue-800 text-xs font-medium ml-1 px-2 py-0.5 rounded-full dark:bg-blue-900 dark:text-blue-300">1</span></button>
                            <button data-tab="errors-panel" class="log-tab-btn px-4 py-2 text-sm font-medium border-b-2 border-transparent text-gray-500 hover:text-gray-700 dark:hover:text-gray-300">Errors <span class="bg-red-100 text-red-800 text-xs font-medium ml-1 px-2 py-0.5 rounded-full dark:bg-red-900 dark:text-red-300">2</span></button>
                        </div>
                        <div class="flex items-center space-x-2 p-1">
                            <button class="p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700"><i data-lucide="trash-2" class="w-4 h-4"></i></button>
                            <button class="p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700"><i data-lucide="save" class="w-4 h-4"></i></button>
                            <button id="log-toggle" class="p-2 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700"><i data-lucide="chevrons-down-up" class="w-4 h-4"></i></button>
                        </div>
                    </div>
                    <!-- Log Content -->
                    <div class="flex-1 overflow-y-auto p-2 font-mono text-xs">
                        <!-- Console Log Panel -->
                        <div id="log-console-panel" class="log-panel active flex-col space-y-1">
                            <p><span class="text-gray-500">[13:01:01]</span> <span class="text-blue-500">[INFO]</span> <span class="font-bold">[App]</span> Application started.</p>
                            <p><span class="text-gray-500">[13:01:15]</span> <span class="text-blue-500">[INFO]</span> <span class="font-bold">[Clip Combiner]</span> Loading CLIP model ViT-B/32...</p>
                            <p><span class="text-gray-500">[13:01:45]</span> <span class="text-green-500">[SUCCESS]</span> <span class="font-bold">[Clip Combiner]</span> Model loaded successfully.</p>
                            <p><span class="text-gray-500">[13:02:01]</span> <span class="text-yellow-500">[WARNING]</span> <span class="font-bold">[Image Mover]</span> Source folder contains 12 non-image files.</p>
                            <p><span class="text-gray-500">[13:03:10]</span> <span class="text-red-500">[ERROR]</span> <span class="font-bold">[Upscale Remover]</span> Permission denied while deleting file: /path/to/locked_file.png</p>
                            <p><span class="text-gray-500">[13:03:11]</span> <span class="text-red-500">[ERROR]</span> <span class="font-bold">[Upscale Remover]</span> File not found: /path/to/missing_file.jpg</p>
                        </div>
                        <!-- Task Queue Panel -->
                        <div id="task-queue-panel" class="log-panel">
                            <table class="w-full text-left">
                                <thead class="text-gray-400"><tr><th class="p-1">Task</th><th class="p-1">Status</th><th class="p-1 w-1/3">Progress</th><th class="p-1">Elapsed</th></tr></thead>
                                <tbody>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <td class="p-1">Combining 542 Images</td>
                                        <td class="p-1 text-yellow-400">Running</td>
                                        <td class="p-1"><div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5"><div class="bg-blue-600 h-2.5 rounded-full" style="width: 81%"></div></div></td>
                                        <td class="p-1">00:01:32</td>
                                    </tr>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <td class="p-1">Moving Old Images</td>
                                        <td class="p-1 text-gray-400">Queued</td>
                                        <td class="p-1">N/A</td>
                                        <td class="p-1">N/A</td>
                                    </tr>
                                    <tr class="border-b border-gray-200 dark:border-gray-700">
                                        <td class="p-1">Renaming 1000 files</td>
                                        <td class="p-1 text-green-400">Completed</td>
                                        <td class="p-1"><div class="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2.5"><div class="bg-green-600 h-2.5 rounded-full" style="width: 100%"></div></div></td>
                                        <td class="p-1">00:00:15</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!-- Errors Panel -->
                        <div id="errors-panel" class="log-panel flex-col space-y-1">
                            <p><span class="text-gray-500">[13:03:10]</span> <span class="text-red-500">[ERROR]</span> <span class="font-bold">[Upscale Remover]</span> Permission denied while deleting file: /path/to/locked_file.png</p>
                            <p><span class="text-gray-500">[13:03:11]</span> <span class="text-red-500">[ERROR]</span> <span class="font-bold">[Upscale Remover]</span> File not found: /path/to/missing_file.jpg</p>
                        </div>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            // Initialize Lucide Icons
            lucide.createIcons();

            // --- Tool Definitions ---
            const tools = [
                {
                    group: "File & Data Tools",
                    items: [
                        { id: "csv-converter", name: "CSV Converter", icon: "file-text" },
                        { id: "csv-splitter", name: "CSV Splitter", icon: "file-spreadsheet" },
                        { id: "embed-metadata", name: "Embed Metadata", icon: "tag" },
                        { id: "image-renamer", name: "Image Renamer", icon: "text-cursor-input" },
                    ]
                },
                {
                    group: "Conversion & Extraction",
                    items: [
                        { id: "png-to-jpeg", name: "PNG to JPEG", icon: "image" },
                        { id: "unzip-images", name: "Unzip Images", icon: "file-zip" },
                        { id: "image-extractor", name: "Image Extractor", icon: "scissors" },
                    ]
                },
                {
                    group: "Image Manipulation",
                    items: [
                        { id: "image-resizer", name: "Image Resizer", icon: "crop" },
                        { id: "image-splitter", name: "Image Splitter", icon: "layout-grid" },
                        { id: "image-cleaner", name: "Image Cleaner", icon: "sparkles" },
                    ]
                },
                {
                    group: "Advanced Tools",
                    items: [
                        { id: "clip-combiner", name: "Clip Combiner", icon: "combine" },
                        { id: "upscale-remover", name: "Upscale Remover", icon: "trash-2" },
                        { id: "image-mover", name: "Image Mover", icon: "move-right" },
                    ]
                }
            ];

            const navRail = document.getElementById('nav-rail');
            tools.forEach(group => {
                const groupHeader = document.createElement('h3');
                groupHeader.className = 'px-3 pt-4 pb-2 text-xs font-semibold text-gray-500 uppercase tracking-wider';
                groupHeader.textContent = group.group;
                navRail.appendChild(groupHeader);

                group.items.forEach(tool => {
                    const button = document.createElement('button');
                    button.className = 'nav-tool-btn flex items-center w-full text-left px-3 py-2 text-sm font-medium rounded-md text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700';
                    button.dataset.tool = `tool-${tool.id}`;
                    button.innerHTML = `<i data-lucide="${tool.icon}" class="w-5 h-5 mr-3 flex-shrink-0"></i><span class="flex-grow">${tool.name}</span>`;
                    navRail.appendChild(button);
                });
            });
            lucide.createIcons(); // Re-initialize icons after adding them

            // --- Theme Toggling ---
            const themeToggle = document.getElementById('theme-toggle');
            themeToggle.addEventListener('click', () => {
                document.documentElement.classList.toggle('dark');
            });

            // --- Tool Switching Logic ---
            const toolButtons = document.querySelectorAll('.nav-tool-btn');
            const toolWorkspaces = document.querySelectorAll('.tool-workspace');
            let activeToolButton = null;

            toolButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Deactivate previous button
                    if(activeToolButton) {
                        activeToolButton.classList.remove('bg-blue-100', 'dark:bg-blue-900/50', 'text-blue-600', 'dark:text-blue-400');
                    }
                    // Activate current button
                    button.classList.add('bg-blue-100', 'dark:bg-blue-900/50', 'text-blue-600', 'dark:text-blue-400');
                    activeToolButton = button;

                    const toolId = button.dataset.tool;
                    toolWorkspaces.forEach(workspace => {
                        workspace.classList.toggle('active', workspace.id === toolId);
                    });
                });
            });
            
            // Activate first tool by default
            if(toolButtons.length > 0) {
                 // Find a tool that has a real workspace and click it
                 const firstRealTool = Array.from(toolButtons).find(btn => document.getElementById(btn.dataset.tool));
                 if (firstRealTool) {
                    firstRealTool.click();
                 }
            }


            // --- Log Console Logic ---
            const logTabs = document.getElementById('log-tabs');
            const logTabButtons = logTabs.querySelectorAll('.log-tab-btn');
            const logPanels = document.querySelectorAll('.log-panel');
            
            logTabs.addEventListener('click', (e) => {
                const button = e.target.closest('button');
                if (!button) return;

                logTabButtons.forEach(btn => {
                    btn.classList.remove('border-blue-500', 'text-blue-500');
                    btn.classList.add('border-transparent', 'text-gray-500');
                });
                button.classList.add('border-blue-500', 'text-blue-500');
                button.classList.remove('border-transparent', 'text-gray-500');

                const tabId = button.dataset.tab;
                logPanels.forEach(panel => {
                    panel.classList.toggle('active', panel.id === tabId);
                });
            });

            const logToggle = document.getElementById('log-toggle');
            const logConsole = document.getElementById('log-console');
            logToggle.addEventListener('click', () => {
                logConsole.classList.toggle('h-64');
                logConsole.classList.toggle('h-10');
            });

            // --- Resizer Logic ---
            const makeResizable = (resizer, elementToResize, direction) => {
                let isResizing = false;

                resizer.addEventListener('mousedown', (e) => {
                    isResizing = true;
                    document.body.style.cursor = direction === 'y' ? 'ns-resize' : 'ew-resize';
                    document.body.style.userSelect = 'none';
                });

                document.addEventListener('mousemove', (e) => {
                    if (!isResizing) return;
                    if (direction === 'y') {
                        const newHeight = window.innerHeight - e.clientY;
                        elementToResize.style.height = `${Math.max(40, Math.min(newHeight, window.innerHeight - 200))}px`;
                    } else {
                        const newWidth = e.clientX;
                        elementToResize.style.width = `${Math.max(150, Math.min(newWidth, window.innerWidth - 300))}px`;
                    }
                });

                document.addEventListener('mouseup', () => {
                    isResizing = false;
                    document.body.style.cursor = 'default';
                    document.body.style.userSelect = 'auto';
                });
            };

            makeResizable(document.getElementById('log-resizer'), logConsole, 'y');
            makeResizable(document.getElementById('nav-resizer'), navRail, 'x');

        });
    </script>
</body>
</html>
