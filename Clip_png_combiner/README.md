# Image Variation Combiner

A comprehensive Python toolkit that combines multiple image variations into single composite images. Features both manual combination and **automatic variation detection** capabilities. Perfect for creating image grids, collages, or comparing different versions of images.

## 🆕 NEW: CLIP-Powered Similarity Detection

The toolkit now features **AI-powered image similarity detection** using OpenAI's CLIP model, providing:
- **🤖 CLIP Integration**: Semantic understanding of image content for highly accurate similarity detection
- **📐 Aspect Ratio Grouping**: Images are grouped by aspect ratio before similarity detection (prevents comparing 16:9 with 4:3 images)
- **🎯 Adjustable Thresholds**: Fine-tune similarity sensitivity and aspect ratio tolerance for your specific needs
- **📊 Real-time Processing**: Monitor progress with detailed logging and aspect ratio group information

### Previous Features:
- **Automatically find** image variations in directories
- **Group similar images** based on visual similarity  
- **Create batches** of 4 (or custom size) similar images
- **Process entire folders** without manual selection

## Features

- **Multiple Layout Options**: Grid, horizontal, and vertical arrangements
- **Automatic Resizing**: Uniform sizing with aspect ratio preservation
- **Customizable Spacing**: Adjustable gaps between images
- **Background Colors**: Configurable background colors
- **Flexible Grid**: Auto-calculated or custom column counts
- **High Quality Output**: Maintains image quality during processing

## Installation

### Quick Setup

1. **Clone or download this repository**

2. **Install basic dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Install CLIP dependencies (recommended for best accuracy):**
   ```bash
   python install_clip.py
   ```
   
   Or manually:
   ```bash
   pip install torch torchvision
   pip install git+https://github.com/openai/CLIP.git
   pip install scikit-learn numpy
   ```

### System Requirements
- **Python**: 3.7 or higher
- **Memory**: 4GB+ RAM (8GB+ recommended for CLIP)
- **GPU**: Optional but recommended for faster CLIP processing

## Usage

### 🖥️ GUI Application (Recommended)

```bash
python image_combiner_gui.py
```

**New GUI Features:**
- **Similarity Method Selection**: Toggle between CLIP (AI-based) and imagehash (fast)
- **CLIP Threshold Control**: Adjust similarity sensitivity (0.5=loose, 0.95=strict)
- **Real-time Processing Log**: Monitor progress and see detailed information
- **Visual Settings**: Layout, spacing, quality, and background options

### 🤖 Command Line: CLIP-Powered Detection

```bash
# Use CLIP for high-accuracy similarity detection with aspect ratio grouping (default)
python auto_variation_finder.py /path/to/image/folder

# Specify output directory and CLIP threshold
python auto_variation_finder.py /path/to/images -o output_batches --clip-threshold 0.8

# Custom batch size with CLIP and aspect ratio grouping
python auto_variation_finder.py /path/to/images --batch-size 6 --clip-threshold 0.85

# Adjust aspect ratio tolerance (how similar aspect ratios need to be to group together)
python auto_variation_finder.py /path/to/images --aspect-ratio-tolerance 0.1  # More loose grouping
python auto_variation_finder.py /path/to/images --aspect-ratio-tolerance 0.02  # Stricter grouping

# Fine-tune similarity detection
python auto_variation_finder.py /path/to/images --clip-threshold 0.9  # Very strict CLIP
python auto_variation_finder.py /path/to/images --clip-threshold 0.7  # More loose CLIP

# Specify file extensions to process
python auto_variation_finder.py /path/to/images --extensions .jpg .png .tiff
```

#### How Automatic Detection Works

1. **Scans Directory**: Recursively finds all image files
2. **Groups by Aspect Ratio**: Images are first grouped by their width-to-height ratio (e.g., 16:9, 4:3, 1:1)
3. **Extracts CLIP Features**: Uses AI-powered CLIP model to understand image content semantically
4. **Groups Similar Images**: Compares CLIP features within each aspect ratio group to find variations
5. **Creates Batches**: Groups similar images into batches of specified size
6. **Combines Images**: Automatically creates composite images for each batch
7. **Generates Reports**: Creates text files listing source images for each batch

#### Aspect Ratio Grouping

The tool now groups images by aspect ratio **before** performing similarity detection. This ensures that:
- **16:9 landscape images** are only compared with other 16:9 images
- **4:3 portrait images** are only compared with other 4:3 images
- **Square (1:1) images** are only compared with other square images
- And so on...

This prevents inappropriate comparisons between images of fundamentally different dimensions and focuses similarity analysis on images with matching proportions.

#### Demo the Automatic Finder

```bash
# Run interactive demo with sample images
python demo_auto_finder.py

# Clean up demo files
python demo_auto_finder.py cleanup
```

### 📝 Manual Combination

### Basic Usage

```bash
# Combine images in a grid layout
python image_combiner.py image1.jpg image2.jpg image3.jpg image4.jpg

# Specify output file
python image_combiner.py *.jpg -o my_combined_image.png
```

### Layout Options

```bash
# Grid layout (default)
python image_combiner.py *.jpg --layout grid

# Horizontal layout
python image_combiner.py *.jpg --layout horizontal

# Vertical layout
python image_combiner.py *.jpg --layout vertical
```

### Grid Customization

```bash
# Specify number of columns
python image_combiner.py *.jpg --layout grid --cols 3

# Auto-calculate square grid (default)
python image_combiner.py *.jpg --layout grid
```

### Sizing and Spacing

```bash
# Custom spacing between images
python image_combiner.py *.jpg --spacing 20

# Resize all images to specific dimensions
python image_combiner.py *.jpg --resize 300x300

# Keep original sizes (no uniform resizing)
python image_combiner.py *.jpg --no-uniform
```

### Styling

```bash
# Custom background color
python image_combiner.py *.jpg --background black
python image_combiner.py *.jpg --background "#FF5733"
```

### Complete Example

```bash
python image_combiner.py photo1.jpg photo2.jpg photo3.jpg photo4.jpg photo5.jpg photo6.jpg \
  --layout grid \
  --cols 3 \
  --spacing 15 \
  --resize 400x400 \
  --background white \
  --output gallery.png
```

## Command Line Arguments

| Argument | Short | Description | Default |
|----------|-------|-------------|---------|
| `images` | - | Input image files (required) | - |
| `--output` | `-o` | Output file name | `combined_image.jpg` |
| `--layout` | `-l` | Layout type: grid, horizontal, vertical | `grid` |
| `--cols` | `-c` | Number of columns for grid layout | Auto-calculated |
| `--spacing` | `-s` | Spacing between images in pixels | `10` |
| `--background` | `-bg` | Background color | `white` |
| `--resize` | `-r` | Resize images to WIDTHxHEIGHT | Auto-sized |
| `--no-uniform` | - | Don't resize images to uniform size | `False` |

## Supported Image Formats

The tool supports all image formats supported by Pillow, including:
- JPEG (.jpg, .jpeg)
- PNG (.png)
- BMP (.bmp)
- TIFF (.tiff, .tif)
- GIF (.gif)
- WebP (.webp)

## Examples

### Creating a Photo Grid

```bash
# Create a 2x2 grid of vacation photos
python image_combiner.py vacation1.jpg vacation2.jpg vacation3.jpg vacation4.jpg \
  --layout grid --cols 2 --spacing 20 --resize 500x500 --output vacation_grid.jpg
```

### Before/After Comparison

```bash
# Horizontal comparison of before and after images
python image_combiner.py before.jpg after.jpg \
  --layout horizontal --spacing 30 --background black --output comparison.png
```

### Vertical Timeline

```bash
# Create a vertical timeline of progress images
python image_combiner.py step1.jpg step2.jpg step3.jpg step4.jpg \
  --layout vertical --spacing 25 --resize 600x400 --output timeline.jpg
```

## Tips

1. **Image Quality**: Use PNG output for better quality when combining images with transparency
2. **Performance**: For large numbers of images, consider resizing to smaller dimensions
3. **Aspect Ratios**: The tool automatically handles different aspect ratios by centering images
4. **File Paths**: Use absolute paths or ensure images are in the current directory
5. **Batch Processing**: Use shell wildcards (*.jpg) to process multiple files easily

## Error Handling

The tool includes robust error handling:
- Skips missing or corrupted image files
- Provides informative error messages
- Continues processing valid images even if some fail

## License

This project is open source and available under the MIT License.