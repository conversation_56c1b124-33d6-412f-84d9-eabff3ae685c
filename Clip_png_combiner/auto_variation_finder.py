#!/usr/bin/env python3
"""
Automatic Image Variation Finder and Combiner

A Python tool that automatically finds similar image variations and combines them
into batches of 4 images per composite. Images are first grouped by aspect ratio
to ensure similarity detection only occurs between images with matching proportions.
"""

import os
import sys
from PIL import Image
import argparse
from typing import List, Tuple, Dict, Set, Optional
from collections import defaultdict
import glob
from image_combiner import ImageCombiner
import shutil
import numpy as np
try:
    import torch
    import clip
    from sklearn.metrics.pairwise import cosine_similarity
    CLIP_AVAILABLE = True
except ImportError:
    CLIP_AVAILABLE = False
    print("Error: CLIP not available. Install with: pip install torch torchvision clip-by-openai scikit-learn")
    print("CLIP is required for this application.")
    sys.exit(1)

class ImageVariationFinder:
    def __init__(self, clip_threshold: float = 0.85, clip_model_name: str = "ViT-B/32", device: str = "auto", aspect_ratio_tolerance: float = 0.05):
        """
        Initialize the variation finder.

        Args:
            clip_threshold: Minimum cosine similarity for CLIP (0.0-1.0, higher = more strict)
            clip_model_name: CLIP model to use ("ViT-B/32", "ViT-L/14", "RN50", etc.)
            device: Device to use ("auto", "cpu", "gpu", "mps", "cuda")
            aspect_ratio_tolerance: Tolerance for grouping similar aspect ratios (default: 0.05)
        """
        self.clip_threshold = clip_threshold
        self.clip_model_name = clip_model_name
        self.aspect_ratio_tolerance = aspect_ratio_tolerance
        self.image_features = {}
        self.image_paths = []
        
        # Initialize CLIP model
        if not CLIP_AVAILABLE:
            raise RuntimeError("CLIP is required but not available. Install with: pip install torch torchvision clip-by-openai scikit-learn")
        
        try:
            print(f"Loading CLIP model: {self.clip_model_name}...")
            self.device = self._select_device(device)
            self.clip_model, self.clip_preprocess = clip.load(self.clip_model_name, device=self.device)
            print(f"CLIP model {self.clip_model_name} loaded on {self.device}")
        except Exception as e:
            raise RuntimeError(f"Failed to load CLIP model: {e}")
    
    def _select_device(self, device_preference: str) -> str:
        """
        Select the appropriate device based on preference and availability.
        
        Args:
            device_preference: User's device preference
            
        Returns:
            str: Selected device name
        """
        if device_preference == "cpu":
            return "cpu"
        elif device_preference == "auto":
            # Auto-select best available device
            if torch.backends.mps.is_available():
                return "mps"
            elif torch.cuda.is_available():
                return "cuda"
            else:
                return "cpu"
        elif device_preference in ["gpu", "mps"]:
            if torch.backends.mps.is_available():
                return "mps"
            else:
                print("Warning: MPS not available, falling back to CPU")
                return "cpu"
        elif device_preference == "cuda":
            if torch.cuda.is_available():
                return "cuda"
            else:
                print("Warning: CUDA not available, falling back to CPU")
                return "cpu"
        else:
            print(f"Warning: Unknown device '{device_preference}', using auto-selection")
            return self._select_device("auto")
    

    
    def extract_clip_features(self, image_path: str) -> np.ndarray:
        """
        Extract CLIP features for an image.
        
        Args:
            image_path: Path to the image file
            
        Returns:
            CLIP feature vector as numpy array
        """
        try:
            with Image.open(image_path) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Preprocess image for CLIP
                image_input = self.clip_preprocess(img).unsqueeze(0).to(self.device)
                
                # Extract features
                with torch.no_grad():
                    image_features = self.clip_model.encode_image(image_input)
                    # Normalize features
                    image_features = image_features / image_features.norm(dim=-1, keepdim=True)
                
                return image_features.cpu().numpy().flatten()
        except Exception as e:
            print(f"Error extracting CLIP features from {image_path}: {e}")
            return None
    
    def scan_directory(self, directory: str, extensions: List[str] = None) -> List[str]:
        """
        Scan directory for image files.
        
        Args:
            directory: Directory to scan
            extensions: List of file extensions to include
            
        Returns:
            List of image file paths
        """
        if extensions is None:
            extensions = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif', '.webp']
        
        image_files = []
        for ext in extensions:
            pattern = os.path.join(directory, f"**/*{ext}")
            image_files.extend(glob.glob(pattern, recursive=True))
            pattern = os.path.join(directory, f"**/*{ext.upper()}")
            image_files.extend(glob.glob(pattern, recursive=True))
        
        return sorted(list(set(image_files)))
    
    def find_similar_images(self, image_paths: List[str]) -> Dict[str, List[str]]:
        """
        Find groups of similar images using CLIP, grouped by aspect ratio.

        Images are first grouped by their aspect ratio (width-to-height ratio) to ensure
        that similarity detection only occurs between images with matching proportions.
        This prevents comparing images of different dimensions (e.g., 16:9 landscape
        images won't be compared with 4:3 portrait images).

        Args:
            image_paths: List of image file paths

        Returns:
            Dictionary mapping representative image to list of similar images
        """
        print(f"Analyzing {len(image_paths)} images for similarity with aspect ratio grouping...")
        return self._find_similar_images_clip(image_paths)
    
    def _get_aspect_ratio_group(self, image_path: str) -> str:
        """
        Get the aspect ratio group for an image.

        Args:
            image_path: Path to the image file

        Returns:
            String representing the aspect ratio group
        """
        try:
            with Image.open(image_path) as img:
                width, height = img.size
                aspect_ratio = width / height

                # Round to reduce the number of groups for very similar aspect ratios
                # Use tolerance to group similar aspect ratios together
                tolerance = self.aspect_ratio_tolerance
                rounded_ratio = round(aspect_ratio / tolerance) * tolerance

                # Create a readable group name
                if abs(rounded_ratio - 1.0) < tolerance:
                    return "1:1 (Square)"
                elif abs(rounded_ratio - 4/3) < tolerance:
                    return "4:3 (Standard)"
                elif abs(rounded_ratio - 16/9) < tolerance:
                    return "16:9 (Widescreen)"
                elif abs(rounded_ratio - 3/2) < tolerance:
                    return "3:2 (Classic)"
                elif abs(rounded_ratio - 2/1) < tolerance:
                    return "2:1 (Panoramic)"
                else:
                    return f"{rounded_ratio:.2f}:1"
        except Exception as e:
            print(f"Error getting aspect ratio for {image_path}: {e}")
            return "unknown"

    def _find_similar_images_clip(self, image_paths: List[str]) -> Dict[str, List[str]]:
        """
        Find similar images using CLIP features, grouped by aspect ratio.
        """
        print("Using CLIP for similarity detection with aspect ratio grouping...")

        # First, group images by aspect ratio
        print("Grouping images by aspect ratio...")
        aspect_ratio_groups = defaultdict(list)

        for path in image_paths:
            aspect_group = self._get_aspect_ratio_group(path)
            aspect_ratio_groups[aspect_group].append(path)

        print(f"Found {len(aspect_ratio_groups)} aspect ratio groups:")
        for aspect_group, paths in aspect_ratio_groups.items():
            print(f"  {aspect_group}: {len(paths)} images")

        # Process each aspect ratio group separately
        all_groups = {}

        for aspect_group, group_paths in aspect_ratio_groups.items():
            if len(group_paths) < 2:
                print(f"Skipping aspect ratio group '{aspect_group}' - only {len(group_paths)} image(s)")
                # Still add single images to ensure they're processed
                if len(group_paths) == 1:
                    all_groups[group_paths[0]] = group_paths
                continue

            print(f"\nProcessing aspect ratio group '{aspect_group}' with {len(group_paths)} images...")

            # Extract CLIP features for images in this aspect ratio group
            valid_images = []
            feature_matrix = []

            for i, path in enumerate(group_paths):
                print(f"  Processing image {i+1}/{len(group_paths)}: {os.path.basename(path)}")
                features = self.extract_clip_features(path)
                if features is not None:
                    self.image_features[path] = features
                    valid_images.append(path)
                    feature_matrix.append(features)
                else:
                    print(f"  Skipping invalid image: {path}")

            if len(valid_images) < 2:
                print(f"  Not enough valid images in aspect ratio group '{aspect_group}' for similarity comparison")
                # Add single valid images as their own groups
                for path in valid_images:
                    all_groups[path] = [path]
                continue

            print(f"  Successfully processed {len(valid_images)} images in aspect ratio group '{aspect_group}'")

            # Convert to numpy array for efficient computation
            feature_matrix = np.array(feature_matrix)

            # Calculate cosine similarity matrix for this aspect ratio group
            print(f"  Calculating similarity matrix for aspect ratio group '{aspect_group}'...")
            similarity_matrix = cosine_similarity(feature_matrix)

            # Group similar images within this aspect ratio group
            processed = set()

            print(f"  Starting similarity grouping within aspect ratio group '{aspect_group}' (threshold: {self.clip_threshold})")

            # Process all images in this aspect ratio group
            for i, img1_path in enumerate(valid_images):
                if img1_path in processed:
                    continue

                # Start a new group with this image
                current_group = [img1_path]
                processed.add(img1_path)

                # Find similar images within the same aspect ratio group
                for j, img2_path in enumerate(valid_images):
                    if i == j or img2_path in processed:
                        continue

                    # Check cosine similarity
                    similarity = similarity_matrix[i][j]

                    if similarity >= self.clip_threshold:
                        current_group.append(img2_path)
                        processed.add(img2_path)

                # Add the group (even single images to ensure all are processed)
                if len(current_group) > 1:
                    all_groups[img1_path] = current_group
                    avg_similarity = np.mean([similarity_matrix[i][valid_images.index(img)]
                                            for img in current_group[1:]])
                    print(f"  Found group of {len(current_group)} similar images in '{aspect_group}' (avg similarity: {avg_similarity:.3f}, representative: {os.path.basename(img1_path)})")
                else:
                    # Add single images as their own group
                    all_groups[img1_path] = current_group
                    print(f"  Created single-image group in '{aspect_group}' for: {os.path.basename(img1_path)}")

        print(f"\nTotal similarity groups created across all aspect ratios: {len(all_groups)}")
        return dict(all_groups)
    

    
    def create_batches(self, similar_groups: Dict[str, List[str]], batch_size: int = 4) -> List[List[str]]:
        """
        Create batches of images from similar groups based on similarity.
        Each batch contains similar images with a maximum of batch_size images.
        
        Args:
            similar_groups: Dictionary of similar image groups
            batch_size: Maximum number of images per batch (used as limit, not target)
            
        Returns:
            List of batches, each containing similar images (max batch_size per batch)
        """
        batches = []
        
        print(f"Creating batches from {len(similar_groups)} groups with max batch size {batch_size}")
        total_images = sum(len(group) for group in similar_groups.values())
        print(f"Total images to be processed: {total_images}")
        
        for representative, group in similar_groups.items():
            # If group has more than batch_size images, split into multiple batches
            # but keep similar images together
            if len(group) > batch_size:
                # Split large groups into smaller batches of max batch_size
                for i in range(0, len(group), batch_size):
                    batch = group[i:i + batch_size]
                    batches.append(batch)
                    print(f"Created batch {len(batches)} with {len(batch)} similar images (split from larger group)")
            else:
                # Use the entire group as one batch
                # Include single-image groups as well to ensure all images are processed
                batches.append(group)
                if len(group) == 1:
                    print(f"Created batch {len(batches)} with 1 image (will be preserved as-is)")
                else:
                    print(f"Created batch {len(batches)} with {len(group)} images")
        
        total_batched_images = sum(len(batch) for batch in batches)
        print(f"Total images in batches: {total_batched_images}")
        print(f"Created {len(batches)} batches total")
        
        return batches
    
    def process_directory(self, input_dir: str, output_dir: str, batch_size: int = 4, 
                         target_size: Optional[Tuple[int, int]] = (300, 300), layout_mode: str = "grid", 
                         spacing: int = 15, background_color: str = "white", quality: int = 95,
                         clip_threshold: float = None, generate_master_file: bool = False):
        """
        Process a directory to find variations and create combined images.
        
        Args:
            input_dir: Input directory containing images
            output_dir: Output directory for combined images
            batch_size: Number of images per batch
            target_size: Target size for individual images (width, height), or None for dynamic sizing
            layout_mode: Layout mode ('grid', 'horizontal', 'vertical')
            spacing: Spacing between images in pixels
            background_color: Background color for combined images
            quality: JPEG quality (70-100)
            clip_threshold: Override clip_threshold for this operation
            generate_master_file: Whether to generate a master text file with all batch info
        """
        # Override settings if provided
        if clip_threshold is not None:
            original_clip_threshold = self.clip_threshold
            self.clip_threshold = clip_threshold
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Scan for images
        print(f"Scanning directory: {input_dir}")
        image_paths = self.scan_directory(input_dir)
        
        if not image_paths:
            print("No images found in the specified directory")
            return []
        
        print(f"Found {len(image_paths)} image files")
        
        # Track all processed images to handle unprocessed ones later
        all_processed_images = set()
        
        # Find similar images
        similar_groups = self.find_similar_images(image_paths)
        
        if not similar_groups:
            print("No similar image groups found")
            # Don't return yet, process individual images below
        else:
            print(f"Found {len(similar_groups)} groups of similar images")
        
        # Create batches
        batches = self.create_batches(similar_groups, batch_size)
        
        if not batches:
            print("No valid batches created")
            # Don't return yet, process individual images below
        else:
            print(f"Created {len(batches)} batches for processing")
        
        # Combine images in each batch
        combiner = ImageCombiner(spacing=spacing, background_color=background_color)
        
        batch_counter = 0
        
        for i, batch in enumerate(batches, 1):
            try:
                print(f"\nProcessing batch {i}/{len(batches)} ({len(batch)} images)...")
                
                # Add all images in this batch to the processed set
                for img_path in batch:
                    all_processed_images.add(img_path)
                
                # For single-image batches, preserve the image as-is without combining
                if len(batch) == 1:
                    img_path = batch[0]
                    batch_counter += 1
                    
                    # Save image with original format, maintaining exact same image and size
                    original_format = os.path.splitext(img_path)[1].lower()
                    if original_format in ['.png', '.gif', '.webp', '.tiff', '.tif'] or background_color.lower() == "transparent":
                        output_filename = f"variation_batch_{batch_counter:03d}{original_format}"
                    else:
                        output_filename = f"variation_batch_{batch_counter:03d}.jpg"
                    
                    output_path = os.path.join(output_dir, output_filename)
                    
                    # Copy the original file directly to preserve the exact image with its original size
                    shutil.copy2(img_path, output_path)
                    
                    print(f"Saved individual image as-is: {output_filename} (original size preserved)")
                    
                    # Create a text file listing the source image
                    info_filename = f"variation_batch_{batch_counter:03d}_sources.txt"
                    info_path = os.path.join(output_dir, info_filename)
                    with open(info_path, 'w') as f:
                        f.write(f"Batch {batch_counter} - Source Image:\n")
                        f.write("=" * 30 + "\n")
                        f.write(f"1. {os.path.relpath(img_path, input_dir)}\n")
                    
                    continue  # Skip the rest of the loop for single images
                
                # For multi-image batches, combine them as before
                # Load images
                images = combiner.load_images(batch)
                
                if not images:
                    print(f"Failed to load images for batch {i}")
                    continue
                
                # Resize to uniform size
                images = combiner.resize_images_uniform(images, target_size=target_size)
                
                # Combine images based on layout mode
                if layout_mode == "grid":
                    combined = combiner.combine_grid(images)  # Let it auto-determine optimal layout
                elif layout_mode == "horizontal":
                    combined = combiner.combine_horizontal(images)
                elif layout_mode == "vertical":
                    combined = combiner.combine_vertical(images)
                else:
                    combined = combiner.combine_grid(images)  # Default to grid with optimal layout
                
                # Save combined image
                # Use PNG for transparent backgrounds, JPG for others
                batch_counter += 1
                if background_color.lower() == "transparent":
                    output_filename = f"variation_batch_{batch_counter:03d}.png"
                    output_path = os.path.join(output_dir, output_filename)
                    combined.save(output_path)
                else:
                    output_filename = f"variation_batch_{batch_counter:03d}.jpg"
                    output_path = os.path.join(output_dir, output_filename)
                    
                    # Convert RGBA to RGB if needed for JPEG format
                    if combined.mode == 'RGBA':
                        # Create a white background and paste the RGBA image onto it
                        rgb_image = Image.new('RGB', combined.size, (255, 255, 255))
                        rgb_image.paste(combined, mask=combined.split()[-1])  # Use alpha channel as mask
                        rgb_image.save(output_path, quality=quality)
                    else:
                        combined.save(output_path, quality=quality)
                
                print(f"Saved: {output_filename} ({combined.size[0]}x{combined.size[1]})")
                
                # Create a text file listing the source images
                info_filename = f"variation_batch_{batch_counter:03d}_sources.txt"
                info_path = os.path.join(output_dir, info_filename)
                with open(info_path, 'w') as f:
                    f.write(f"Batch {batch_counter} - Source Images:\n")
                    f.write("=" * 30 + "\n")
                    for j, img_path in enumerate(batch, 1):
                        f.write(f"{j}. {os.path.relpath(img_path, input_dir)}\n")
                
            except Exception as e:
                print(f"Error processing batch {i}: {e}")
                continue
        
        # Process any unprocessed images individually (no similarity match)
        unprocessed = [path for path in image_paths if path not in all_processed_images]
        if unprocessed:
            print(f"\nProcessing {len(unprocessed)} remaining images with no similarity matches...")
            
            # Process each unprocessed image individually
            for i, img_path in enumerate(unprocessed):
                try:
                    # Create a batch with just this one image
                    single_batch = [img_path]
                    
                    # Since it's a single image with no matches, preserve it exactly as is
                    batch_counter += 1
                    
                    # Save image with original format, maintaining exact same image and size
                    original_format = os.path.splitext(img_path)[1].lower()
                    if original_format in ['.png', '.gif', '.webp', '.tiff', '.tif'] or background_color.lower() == "transparent":
                        output_filename = f"variation_batch_{batch_counter:03d}{original_format}"
                    else:
                        output_filename = f"variation_batch_{batch_counter:03d}.jpg"
                    
                    output_path = os.path.join(output_dir, output_filename)
                    
                    # Copy the original file directly to preserve the exact image with its original size
                    shutil.copy2(img_path, output_path)
                    
                    print(f"Saved individual image as-is: {output_filename} (original size preserved)")
                    
                    # Create a text file listing the source image
                    info_filename = f"variation_batch_{batch_counter:03d}_sources.txt"
                    info_path = os.path.join(output_dir, info_filename)
                    with open(info_path, 'w') as f:
                        f.write(f"Batch {batch_counter} - Source Image:\n")
                        f.write("=" * 30 + "\n")
                        f.write(f"1. {os.path.relpath(img_path, input_dir)}\n")
                        
                except Exception as e:
                    print(f"Error processing individual image {img_path}: {e}")
                    continue
        
        # Generate a master text file with all batch information if requested
        if generate_master_file:
            master_filename = "all_batches_info.txt"
            master_path = os.path.join(output_dir, master_filename)
            with open(master_path, 'w') as f:
                f.write(f"Image Variation Batches - Master File\n")
                f.write(f"Total images processed: {len(image_paths)}\n")
                f.write(f"Total batches created: {batch_counter}\n")
                f.write(f"Similarity threshold: {self.clip_threshold}\n")
                f.write(f"CLIP model: {self.clip_model_name}\n")
                f.write("=" * 60 + "\n\n")
                
                # Write info about each batch
                for i in range(1, batch_counter + 1):
                    batch_file_path = os.path.join(output_dir, f"variation_batch_{i:03d}_sources.txt")
                    if os.path.exists(batch_file_path):
                        f.write(f"Batch #{i:03d}\n")
                        f.write("-" * 30 + "\n")
                        
                        # Copy content from the individual batch file
                        with open(batch_file_path, 'r') as batch_file:
                            # Skip the first two lines (header)
                            next(batch_file)  # Skip "Batch X - Source Images:"
                            next(batch_file)  # Skip "===================="
                            
                            # Copy the rest of the file
                            for line in batch_file:
                                f.write(line)
                        
                        f.write("\n")
            
            print(f"Generated master text file: {master_filename}")
        
        # Restore original settings if they were overridden
        if clip_threshold is not None:
            self.clip_threshold = original_clip_threshold
            
        print(f"\nProcessing complete! Check the output directory: {output_dir}")
        print(f"Total batches created: {batch_counter}")
        return batches

def main():
    parser = argparse.ArgumentParser(description="Automatically find image variations and combine them into batches using CLIP with aspect ratio grouping")
    parser.add_argument("input_dir", help="Input directory containing images")
    parser.add_argument("-o", "--output", default="variation_batches", help="Output directory for combined images")
    parser.add_argument("-b", "--batch-size", type=int, default=4, help="Number of images per batch")
    parser.add_argument("--clip-threshold", type=float, default=0.85, help="CLIP similarity threshold (0.0-1.0, higher = more strict)")
    parser.add_argument("--clip-model", default="ViT-B/32", help="CLIP model to use (ViT-B/32, ViT-L/14, RN50, etc.)")
    parser.add_argument("--device", default="auto", choices=["auto", "cpu", "gpu", "mps", "cuda"], help="Device to use for processing (auto, cpu, gpu, mps, cuda)")
    parser.add_argument("--aspect-ratio-tolerance", type=float, default=0.05, help="Tolerance for grouping similar aspect ratios (default: 0.05)")
    parser.add_argument("--extensions", nargs="+", help="File extensions to include (e.g., .jpg .png)")
    parser.add_argument("--master-file", action="store_true", help="Generate a master text file with all batch information")

    args = parser.parse_args()

    if not os.path.isdir(args.input_dir):
        print(f"Error: Input directory '{args.input_dir}' does not exist")
        sys.exit(1)

    # Create variation finder
    finder = ImageVariationFinder(
        clip_threshold=args.clip_threshold,
        clip_model_name=args.clip_model,
        device=args.device,
        aspect_ratio_tolerance=args.aspect_ratio_tolerance
    )
    
    # Process directory
    finder.process_directory(
        input_dir=args.input_dir,
        output_dir=args.output,
        batch_size=args.batch_size,
        generate_master_file=args.master_file
    )

if __name__ == "__main__":
    main()