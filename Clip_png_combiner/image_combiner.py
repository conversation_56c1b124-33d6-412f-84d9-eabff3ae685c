#!/usr/bin/env python3
"""
Image Variation Combiner Tool

A Python tool to combine multiple image variations into a single composite image.
Supports various layout options including grid, horizontal, and vertical arrangements.
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont
import argparse
from typing import List, Tuple, Optional
import math
import re
import shutil

class ImageCombiner:
    def __init__(self, spacing: int = 10, background_color: str = "white"):
        """
        Initialize the ImageCombiner.
        
        Args:
            spacing: Space between images in pixels
            background_color: Background color for the combined image
        """
        self.spacing = spacing
        self.background_color = background_color
        # Determine if we need RGBA mode for transparency
        self.use_rgba = background_color.lower() == "transparent"
        self.image_mode = "RGBA" if self.use_rgba else "RGB"
        # Set actual background color for PIL
        if self.use_rgba:
            self.pil_background = (255, 255, 255, 0)  # Transparent white
        else:
            self.pil_background = background_color
    
    def load_images(self, image_paths: List[str]) -> List[Image.Image]:
        """
        Load images from file paths.
        
        Args:
            image_paths: List of paths to image files
            
        Returns:
            List of PIL Image objects
        """
        images = []
        for path in image_paths:
            if not os.path.exists(path):
                print(f"Warning: Image not found: {path}")
                continue
            try:
                img = Image.open(path)
                images.append(img)
                print(f"Loaded: {path} ({img.size[0]}x{img.size[1]})")
            except Exception as e:
                print(f"Error loading {path}: {e}")
        return images
    
    def resize_images_uniform(self, images: List[Image.Image], target_size: Optional[Tuple[int, int]] = None) -> List[Image.Image]:
        """
        Resize all images to the same size.
        
        Args:
            images: List of PIL Image objects
            target_size: Target size (width, height). If None, uses the size of the largest image
            
        Returns:
            List of resized images
        """
        if not images:
            return []
        
        if target_size is None:
            # Find the maximum dimensions
            max_width = max(img.size[0] for img in images)
            max_height = max(img.size[1] for img in images)
            target_size = (max_width, max_height)
        
        resized_images = []
        for img in images:
            # Resize while maintaining aspect ratio (no cropping)
            img_resized = img.copy()
            img_resized.thumbnail(target_size, Image.Resampling.LANCZOS)
            
            # Create a new image with the target size and paste the resized image
            new_img = Image.new(self.image_mode, target_size, self.pil_background)
            paste_x = (target_size[0] - img_resized.size[0]) // 2
            paste_y = (target_size[1] - img_resized.size[1]) // 2
            
            # Convert source image to match target mode if needed
            if self.use_rgba and img_resized.mode != 'RGBA':
                img_resized = img_resized.convert('RGBA')
            elif not self.use_rgba and img_resized.mode == 'RGBA':
                img_resized = img_resized.convert('RGB')
                
            # Handle transparency properly when pasting
            if img_resized.mode == 'RGBA' and self.use_rgba:
                new_img.paste(img_resized, (paste_x, paste_y), img_resized)
            else:
                new_img.paste(img_resized, (paste_x, paste_y))
            resized_images.append(new_img)
        
        return resized_images
    
    def combine_grid(self, images: List[Image.Image], cols: Optional[int] = None) -> Image.Image:
        """
        Combine images in a grid layout.
        
        Args:
            images: List of PIL Image objects
            cols: Number of columns. If None, creates an optimal grid layout
            
        Returns:
            Combined image
        """
        if not images:
            raise ValueError("No images to combine")
        
        num_images = len(images)
        if cols is None:
            # Create optimal grid layout based on number of images
            if num_images == 2:
                cols = 2  # 2x1 grid
            elif num_images == 3:
                cols = 3  # 3x1 grid for better appearance
            elif num_images <= 4:
                cols = 2  # 2x2 grid
            elif num_images <= 6:
                cols = 3  # 3x2 grid
            elif num_images <= 9:
                cols = 3  # 3x3 grid
            else:
                cols = math.ceil(math.sqrt(num_images))  # Square-ish for larger numbers
        
        rows = math.ceil(num_images / cols)
        
        # Assume all images are the same size (after resizing)
        img_width, img_height = images[0].size
        
        # Calculate total dimensions
        total_width = cols * img_width + (cols - 1) * self.spacing
        total_height = rows * img_height + (rows - 1) * self.spacing
        
        # Create the combined image
        combined = Image.new(self.image_mode, (total_width, total_height), self.pil_background)
        
        # Paste images
        for i, img in enumerate(images):
            row = i // cols
            col = i % cols
            
            x = col * (img_width + self.spacing)
            y = row * (img_height + self.spacing)
            
            # Handle transparency properly when pasting
            if img.mode == 'RGBA' and self.use_rgba:
                combined.paste(img, (x, y), img)
            else:
                combined.paste(img, (x, y))
        
        return combined
    
    def combine_horizontal(self, images: List[Image.Image]) -> Image.Image:
        """
        Combine images horizontally.
        
        Args:
            images: List of PIL Image objects
            
        Returns:
            Combined image
        """
        if not images:
            raise ValueError("No images to combine")
        
        # Calculate total dimensions
        total_width = sum(img.size[0] for img in images) + (len(images) - 1) * self.spacing
        max_height = max(img.size[1] for img in images)
        
        # Create the combined image
        combined = Image.new(self.image_mode, (total_width, max_height), self.pil_background)
        
        # Paste images
        x_offset = 0
        for img in images:
            y_offset = (max_height - img.size[1]) // 2  # Center vertically
            # Handle transparency properly when pasting
            if img.mode == 'RGBA' and self.use_rgba:
                combined.paste(img, (x_offset, y_offset), img)
            else:
                combined.paste(img, (x_offset, y_offset))
            x_offset += img.size[0] + self.spacing
        
        return combined
    
    def combine_vertical(self, images: List[Image.Image]) -> Image.Image:
        """
        Combine images vertically.
        
        Args:
            images: List of PIL Image objects
            
        Returns:
            Combined image
        """
        if not images:
            raise ValueError("No images to combine")
        
        # Calculate total dimensions
        max_width = max(img.size[0] for img in images)
        total_height = sum(img.size[1] for img in images) + (len(images) - 1) * self.spacing
        
        # Create the combined image
        combined = Image.new(self.image_mode, (max_width, total_height), self.pil_background)
        
        # Paste images
        y_offset = 0
        for img in images:
            x_offset = (max_width - img.size[0]) // 2  # Center horizontally
            # Handle transparency properly when pasting
            if img.mode == 'RGBA' and self.use_rgba:
                combined.paste(img, (x_offset, y_offset), img)
            else:
                combined.paste(img, (x_offset, y_offset))
            y_offset += img.size[1] + self.spacing
        
        return combined

    @staticmethod
    def parse_batch_file(batch_file_path: str, input_dir: Optional[str] = None) -> List[List[str]]:
        """
        Parse a batch file containing image paths organized by batch.
        The file should list image paths with batch separators.
        
        Args:
            batch_file_path: Path to the batch file
            input_dir: Optional input directory to prepend to relative paths
            
        Returns:
            List of batches, each containing image file paths
        """
        if not os.path.exists(batch_file_path):
            raise FileNotFoundError(f"Batch file not found: {batch_file_path}")
            
        batches = []
        current_batch = []
        
        try:
            with open(batch_file_path, 'r') as f:
                batch_pattern = re.compile(r'^Batch\s+#?(\d+)', re.IGNORECASE)
                separator_pattern = re.compile(r'^[-=]{3,}$')
                image_pattern = re.compile(r'^(?:\d+\.\s*)?(.+)$')
                
                for line in f:
                    line = line.strip()
                    if not line:  # Skip empty lines
                        continue
                        
                    # Check if this is a batch header
                    if batch_pattern.match(line) or separator_pattern.match(line):
                        # If we have images in the current batch, add it to batches
                        if current_batch:
                            batches.append(current_batch)
                            current_batch = []
                        continue
                    
                    # Try to extract an image path
                    match = image_pattern.match(line)
                    if match:
                        image_path = match.group(1).strip()
                        if image_path:
                            # Handle relative paths if input_dir is provided
                            if input_dir and not os.path.isabs(image_path):
                                # Check if it's a partial path or just a filename
                                if not os.path.exists(image_path) and input_dir:
                                    image_path = os.path.join(input_dir, image_path)
                            current_batch.append(image_path)
                
                # Add the last batch if it has images
                if current_batch:
                    batches.append(current_batch)
        
        except Exception as e:
            print(f"Error parsing batch file: {e}")
            raise
        
        print(f"Parsed {len(batches)} batches from file, with {sum(len(b) for b in batches)} total images")
        return batches
        
    @staticmethod
    def process_from_batch_file(batch_file_path: str, output_dir: str, input_dir: Optional[str] = None,
                               layout_mode: str = "grid", spacing: int = 10,
                               background_color: str = "white", quality: int = 95,
                               resize: Optional[Tuple[int, int]] = None):
        """
        Process batches defined in a batch file.
        
        Args:
            batch_file_path: Path to the batch file
            output_dir: Output directory
            input_dir: Input directory to look for images if paths in batch file are relative
            layout_mode: Layout mode (grid, horizontal, vertical)
            spacing: Spacing between images
            background_color: Background color
            quality: JPEG quality
            resize: Optional target size for images
        """
        # Create output directory
        os.makedirs(output_dir, exist_ok=True)
        
        # Parse the batch file
        batches = ImageCombiner.parse_batch_file(batch_file_path, input_dir)
        
        if not batches:
            print("No valid batches found in the batch file")
            return
        
        # Create combiner
        combiner = ImageCombiner(spacing=spacing, background_color=background_color)
        
        # Check if input files exist
        valid_batches = []
        for batch_idx, batch in enumerate(batches, 1):
            valid_images = []
            for img_path in batch:
                if os.path.exists(img_path):
                    valid_images.append(img_path)
                else:
                    print(f"Warning: Image not found: {img_path} in batch {batch_idx}")
            
            if valid_images:
                valid_batches.append(valid_images)
            else:
                print(f"Warning: Skipping batch {batch_idx} - no valid images found")
        
        if not valid_batches:
            print("No valid images found in any batch")
            return
        
        print(f"Found {len(valid_batches)} valid batches with {sum(len(b) for b in valid_batches)} valid images")
        
        # Process each batch
        for i, batch in enumerate(valid_batches, 1):
            try:
                print(f"\nProcessing batch {i}/{len(valid_batches)} ({len(batch)} images)...")
                
                # For single-image batches, preserve the image as-is without combining
                if len(batch) == 1:
                    img_path = batch[0]
                    
                    # Save image with original format, maintaining exact same image and size
                    original_format = os.path.splitext(img_path)[1].lower()
                    if original_format in ['.png', '.gif', '.webp', '.tiff', '.tif'] or background_color.lower() == "transparent":
                        output_filename = f"imported_batch_{i:03d}{original_format}"
                    else:
                        output_filename = f"imported_batch_{i:03d}.jpg"
                    
                    output_path = os.path.join(output_dir, output_filename)
                    
                    # Copy the original file directly to preserve the exact image with its original size
                    shutil.copy2(img_path, output_path)
                    
                    print(f"Saved individual image as-is: {output_filename} (original size preserved)")
                    
                    # Create a text file listing the source image
                    info_filename = f"imported_batch_{i:03d}_sources.txt"
                    info_path = os.path.join(output_dir, info_filename)
                    with open(info_path, 'w') as f:
                        f.write(f"Imported Batch {i} - Source Image:\n")
                        f.write("=" * 30 + "\n")
                        f.write(f"1. {img_path}\n")
                    
                    continue  # Skip the rest of the loop for single images
                
                # Load images
                images = combiner.load_images(batch)
                
                if not images:
                    print(f"Failed to load images for batch {i}")
                    continue
                
                # Resize if needed
                if resize:
                    images = combiner.resize_images_uniform(images, resize)
                
                # Combine images based on layout mode
                if layout_mode == "grid":
                    combined = combiner.combine_grid(images)
                elif layout_mode == "horizontal":
                    combined = combiner.combine_horizontal(images)
                elif layout_mode == "vertical":
                    combined = combiner.combine_vertical(images)
                else:
                    combined = combiner.combine_grid(images)
                
                # Save combined image
                if background_color.lower() == "transparent":
                    output_filename = f"imported_batch_{i:03d}.png"
                    output_path = os.path.join(output_dir, output_filename)
                    combined.save(output_path)
                else:
                    output_filename = f"imported_batch_{i:03d}.jpg"
                    output_path = os.path.join(output_dir, output_filename)
                    
                    # Convert RGBA to RGB if needed for JPEG format
                    if combined.mode == 'RGBA':
                        rgb_image = Image.new('RGB', combined.size, (255, 255, 255))
                        rgb_image.paste(combined, mask=combined.split()[-1])
                        rgb_image.save(output_path, quality=quality)
                    else:
                        combined.save(output_path, quality=quality)
                
                print(f"Saved: {output_filename} ({combined.size[0]}x{combined.size[1]})")
                
                # Create a text file listing the source images
                info_filename = f"imported_batch_{i:03d}_sources.txt"
                info_path = os.path.join(output_dir, info_filename)
                with open(info_path, 'w') as f:
                    f.write(f"Imported Batch {i} - Source Images:\n")
                    f.write("=" * 30 + "\n")
                    for j, img_path in enumerate(batch, 1):
                        f.write(f"{j}. {img_path}\n")
                
            except Exception as e:
                print(f"Error processing batch {i}: {e}")
                continue
        
        print(f"\nImport processing complete! Check the output directory: {output_dir}")
        print(f"Processed {len(valid_batches)} batches from import file")

def main():
    parser = argparse.ArgumentParser(description="Combine multiple image variations into one image")
    
    # Create subparsers for different modes
    subparsers = parser.add_subparsers(dest='mode', help='Operation mode')
    
    # Single combine mode
    combine_parser = subparsers.add_parser('combine', help='Combine individual images')
    combine_parser.add_argument("images", nargs="+", help="Input image files")
    combine_parser.add_argument("-o", "--output", default="combined_image.jpg", help="Output file name")
    combine_parser.add_argument("-l", "--layout", choices=["grid", "horizontal", "vertical"], default="grid", help="Layout type")
    combine_parser.add_argument("-c", "--cols", type=int, help="Number of columns for grid layout")
    combine_parser.add_argument("-s", "--spacing", type=int, default=10, help="Spacing between images in pixels")
    combine_parser.add_argument("-bg", "--background", default="white", help="Background color")
    combine_parser.add_argument("-r", "--resize", help="Resize images to WIDTHxHEIGHT (e.g., 300x300)")
    combine_parser.add_argument("--no-uniform", action="store_true", help="Don't resize images to uniform size")
    
    # Import mode
    import_parser = subparsers.add_parser('import', help='Process batches from a text file')
    import_parser.add_argument("batch_file", help="Path to the batch file")
    import_parser.add_argument("-i", "--input", help="Input directory containing the images referenced in the batch file")
    import_parser.add_argument("-o", "--output", default="imported_batches", help="Output directory")
    import_parser.add_argument("-l", "--layout", choices=["grid", "horizontal", "vertical"], default="grid", help="Layout type")
    import_parser.add_argument("-s", "--spacing", type=int, default=10, help="Spacing between images in pixels")
    import_parser.add_argument("-bg", "--background", default="white", help="Background color")
    import_parser.add_argument("-q", "--quality", type=int, default=95, help="JPEG quality (70-100)")
    import_parser.add_argument("-r", "--resize", help="Resize images to WIDTHxHEIGHT (e.g., 300x300)")
    
    args = parser.parse_args()
    
    # Default to combine mode if not specified
    if not args.mode:
        args.mode = 'combine'
    
    if args.mode == 'import':
        # Process from batch file
        resize_target = None
        if args.resize:
            try:
                width, height = map(int, args.resize.split('x'))
                resize_target = (width, height)
            except ValueError:
                print("Error: Invalid resize format. Use WIDTHxHEIGHT (e.g., 300x300)")
                sys.exit(1)
        
        print(f"Importing and processing batches from: {args.batch_file}")
        try:
            ImageCombiner.process_from_batch_file(
                batch_file_path=args.batch_file,
                output_dir=args.output,
                input_dir=args.input,
                layout_mode=args.layout,
                spacing=args.spacing,
                background_color=args.background,
                quality=args.quality,
                resize=resize_target
            )
        except Exception as e:
            print(f"Error processing batch file: {e}")
            sys.exit(1)
            
    else:  # combine mode
        # Original behavior for combining images
        # Create combiner
        combiner = ImageCombiner(spacing=args.spacing, background_color=args.background)
        
        # Load images
        print(f"Loading {len(args.images)} images...")
        images = combiner.load_images(args.images)
        
        if not images:
            print("Error: No valid images found")
            sys.exit(1)
        
        print(f"Successfully loaded {len(images)} images")
        
        # Resize images if needed
        if not args.no_uniform:
            target_size = None
            if args.resize:
                try:
                    width, height = map(int, args.resize.split('x'))
                    target_size = (width, height)
                except ValueError:
                    print("Error: Invalid resize format. Use WIDTHxHEIGHT (e.g., 300x300)")
                    sys.exit(1)
            
            print("Resizing images to uniform size...")
            images = combiner.resize_images_uniform(images, target_size)
        
        # Combine images
        print(f"Combining images using {args.layout} layout...")
        if args.layout == "grid":
            combined = combiner.combine_grid(images, args.cols)
        elif args.layout == "horizontal":
            combined = combiner.combine_horizontal(images)
        elif args.layout == "vertical":
            combined = combiner.combine_vertical(images)
        
        # Save the result
        # Handle transparency properly
        if args.background.lower() == "transparent":
            # For transparent backgrounds, force PNG format if not already specified
            if not args.output.lower().endswith(('.png', '.tiff', '.tif')):
                # Change extension to PNG for transparency support
                base_name = os.path.splitext(args.output)[0]
                args.output = base_name + '.png'
                print(f"Note: Changed output format to PNG to preserve transparency: {args.output}")
            combined.save(args.output)
        else:
            # Convert RGBA to RGB if needed for JPEG format
            if combined.mode == 'RGBA' and args.output.lower().endswith(('.jpg', '.jpeg')):
                # Create a white background and paste the RGBA image onto it
                rgb_image = Image.new('RGB', combined.size, (255, 255, 255))
                rgb_image.paste(combined, mask=combined.split()[-1])  # Use alpha channel as mask
                rgb_image.save(args.output, quality=95)
            else:
                combined.save(args.output, quality=95)
        print(f"Combined image saved as: {args.output}")
        print(f"Final dimensions: {combined.size[0]}x{combined.size[1]}")

if __name__ == "__main__":
    main()