import tkinter as tk
from tkinter import filedialog, messagebox
import ttkbootstrap as ttk
from ttkbootstrap.constants import *
import os
import shutil
from PIL import Image
import threading

class ImageExtractorFrame(ttk.Frame):
    def __init__(self, parent, app):
        super().__init__(parent, padding=15)
        self.app = app
        self.create_widgets()
        self.is_processing = False

    def create_widgets(self):
        # Create a responsive grid layout
        self.columnconfigure(0, weight=1)
        self.rowconfigure(4, weight=1)  # Make the status area expandable
        
        # Header section with title and description
        header_frame = ttk.Frame(self)
        header_frame.grid(row=0, column=0, sticky="ew", padx=5, pady=(0, 15))
        header_frame.columnconfigure(0, weight=1)
        
        # Title with icon
        title_frame = ttk.Frame(header_frame)
        title_frame.grid(row=0, column=0, sticky="w")
        
        title_icon = ttk.Label(
            title_frame,
            text="📦",  # Package emoji
            font=("Helvetica", 24)
        )
        title_icon.pack(side=tk.LEFT, padx=(0, 10))
        
        title_text = ttk.Label(
            title_frame,
            text="Image Extractor",
            font=("Helvetica", 18, "bold"),
            bootstyle="info"
        )
        title_text.pack(side=tk.LEFT)
        
        # Description text
        description_frame = ttk.Frame(header_frame)
        description_frame.grid(row=1, column=0, sticky="ew", pady=(10, 0))
        
        description_label = ttk.Label(
            description_frame,
            text="Extract all images from a folder and its subdirectories into a single destination folder. Supports JPG, JPEG, PNG, GIF, BMP, TIFF, and WEBP formats.",
            wraplength=800,
            justify="left",
            bootstyle="secondary"
        )
        description_label.pack(fill=tk.X)
        
        # Main content area with card styling
        content_frame = ttk.Frame(self, bootstyle="default")
        content_frame.grid(row=1, column=0, sticky="ew", padx=5, pady=15)
        content_frame.columnconfigure(0, weight=1)
        
        # Source folder selection
        source_frame = ttk.LabelFrame(content_frame, text="Source Folder", padding=15)
        source_frame.grid(row=0, column=0, sticky="ew", pady=(0, 15))
        source_frame.columnconfigure(1, weight=1)
        
        source_icon = ttk.Label(
            source_frame,
            text="📁",
            font=("Helvetica", 14)
        )
        source_icon.grid(row=0, column=0, padx=(0, 10))
        
        self.source_var = tk.StringVar()
        source_entry = ttk.Entry(
            source_frame,
            textvariable=self.source_var,
            bootstyle="info",
            font=("Helvetica", 10)
        )
        source_entry.grid(row=0, column=1, sticky="ew", padx=(0, 10))
        
        source_browse_btn = ttk.Button(
            source_frame,
            text="Browse",
            command=self.browse_source_folder,
            bootstyle="outline-info",
            width=12
        )
        source_browse_btn.grid(row=0, column=2)
        
        # Destination folder selection
        dest_frame = ttk.LabelFrame(content_frame, text="Destination Folder", padding=15)
        dest_frame.grid(row=1, column=0, sticky="ew", pady=(0, 15))
        dest_frame.columnconfigure(1, weight=1)
        
        dest_icon = ttk.Label(
            dest_frame,
            text="📂",
            font=("Helvetica", 14)
        )
        dest_icon.grid(row=0, column=0, padx=(0, 10))
        
        self.dest_var = tk.StringVar()
        dest_entry = ttk.Entry(
            dest_frame,
            textvariable=self.dest_var,
            bootstyle="success",
            font=("Helvetica", 10)
        )
        dest_entry.grid(row=0, column=1, sticky="ew", padx=(0, 10))
        
        dest_browse_btn = ttk.Button(
            dest_frame,
            text="Browse",
            command=self.browse_dest_folder,
            bootstyle="outline-success",
            width=12
        )
        dest_browse_btn.grid(row=0, column=2)
        
        # Options frame
        options_frame = ttk.LabelFrame(content_frame, text="Options", padding=15)
        options_frame.grid(row=2, column=0, sticky="ew", pady=(0, 15))
        
        # Copy vs Move option
        self.copy_mode = tk.BooleanVar(value=True)
        copy_check = ttk.Checkbutton(
            options_frame,
            text="Copy files (uncheck to move files)",
            variable=self.copy_mode,
            bootstyle="info"
        )
        copy_check.grid(row=0, column=0, sticky="w", pady=(0, 5))
        
        # Preserve folder structure option
        self.preserve_structure = tk.BooleanVar(value=False)
        structure_check = ttk.Checkbutton(
            options_frame,
            text="Preserve folder structure in destination",
            variable=self.preserve_structure,
            bootstyle="info"
        )
        structure_check.grid(row=1, column=0, sticky="w")
        
        # Action buttons
        button_frame = ttk.Frame(content_frame)
        button_frame.grid(row=3, column=0, pady=20)
        
        self.extract_btn = ttk.Button(
            button_frame,
            text="🚀 Start Extraction",
            command=self.start_extraction,
            bootstyle="success",
            width=20
        )
        self.extract_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_btn = ttk.Button(
            button_frame,
            text="⏹️ Stop",
            command=self.stop_extraction,
            bootstyle="danger",
            width=15,
            state="disabled"
        )
        self.stop_btn.pack(side=tk.LEFT)
        
        # Status and progress area
        status_frame = ttk.LabelFrame(self, text="Status", padding=15)
        status_frame.grid(row=4, column=0, sticky="nsew", padx=5, pady=(15, 0))
        status_frame.columnconfigure(0, weight=1)
        status_frame.rowconfigure(1, weight=1)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            status_frame,
            variable=self.progress_var,
            bootstyle="info-striped",
            length=400
        )
        self.progress_bar.grid(row=0, column=0, sticky="ew", pady=(0, 10))
        
        # Status text area
        status_text_frame = ttk.Frame(status_frame)
        status_text_frame.grid(row=1, column=0, sticky="nsew")
        status_text_frame.columnconfigure(0, weight=1)
        status_text_frame.rowconfigure(0, weight=1)
        
        self.status_text = tk.Text(
            status_text_frame,
            height=8,
            wrap=tk.WORD,
            font=("Consolas", 9),
            bg="#f8f9fa",
            fg="#212529"
        )
        self.status_text.grid(row=0, column=0, sticky="nsew")
        
        # Scrollbar for status text
        scrollbar = ttk.Scrollbar(status_text_frame, orient="vertical", command=self.status_text.yview)
        scrollbar.grid(row=0, column=1, sticky="ns")
        self.status_text.config(yscrollcommand=scrollbar.set)
        
        # Initial status message
        self.update_status("Ready to extract images. Please select source and destination folders.")

    def browse_source_folder(self):
        """Browse for source folder"""
        folder = filedialog.askdirectory(title="Select Source Folder")
        if folder:
            self.source_var.set(folder)
            self.update_status(f"Source folder selected: {folder}")

    def browse_dest_folder(self):
        """Browse for destination folder"""
        folder = filedialog.askdirectory(title="Select Destination Folder")
        if folder:
            self.dest_var.set(folder)
            self.update_status(f"Destination folder selected: {folder}")

    def update_status(self, message):
        """Update status text area"""
        self.status_text.insert(tk.END, f"{message}\n")
        self.status_text.see(tk.END)
        self.status_text.update()
        
        # Also update the main app status bar if available
        if hasattr(self.app, 'update_status'):
            self.app.update_status(message)

    def update_progress(self, progress, message=None):
        """Update progress bar and optionally status message"""
        self.progress_var.set(progress)
        if message:
            self.update_status(message)

    def start_extraction(self):
        """Start the image extraction process"""
        source_folder = self.source_var.get().strip()
        dest_folder = self.dest_var.get().strip()
        
        if not source_folder:
            messagebox.showerror("Error", "Please select a source folder.")
            return
            
        if not dest_folder:
            messagebox.showerror("Error", "Please select a destination folder.")
            return
            
        if not os.path.exists(source_folder):
            messagebox.showerror("Error", "Source folder does not exist.")
            return
            
        if source_folder == dest_folder:
            messagebox.showerror("Error", "Source and destination folders cannot be the same.")
            return
        
        # Create destination folder if it doesn't exist
        try:
            os.makedirs(dest_folder, exist_ok=True)
        except Exception as e:
            messagebox.showerror("Error", f"Could not create destination folder: {str(e)}")
            return
        
        # Update UI state
        self.is_processing = True
        self.extract_btn.config(state="disabled")
        self.stop_btn.config(state="normal")
        
        # Clear status and reset progress
        self.status_text.delete(1.0, tk.END)
        self.progress_var.set(0)
        
        # Start extraction in a separate thread
        self.extraction_thread = threading.Thread(
            target=self.run_extraction,
            args=(source_folder, dest_folder),
            daemon=True
        )
        self.extraction_thread.start()

    def stop_extraction(self):
        """Stop the extraction process"""
        self.is_processing = False
        self.update_status("Stopping extraction...")

    def run_extraction(self, source_folder, dest_folder):
        """Run the image extraction process"""
        try:
            # Supported image extensions
            image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff', '.tif', '.webp'}
            
            # First pass: count total images
            self.update_status("Scanning for images...")
            total_images = 0
            
            for root, dirs, files in os.walk(source_folder):
                if not self.is_processing:
                    break
                for file in files:
                    if any(file.lower().endswith(ext) for ext in image_extensions):
                        total_images += 1
            
            if total_images == 0:
                self.update_status("No images found in the source folder.")
                self._reset_ui()
                return
            
            self.update_status(f"Found {total_images} images to extract.")
            
            # Second pass: extract images
            extracted_count = 0
            error_count = 0
            
            for root, dirs, files in os.walk(source_folder):
                if not self.is_processing:
                    break
                    
                for file in files:
                    if not self.is_processing:
                        break
                        
                    if any(file.lower().endswith(ext) for ext in image_extensions):
                        source_path = os.path.join(root, file)
                        
                        try:
                            # Determine destination path
                            if self.preserve_structure.get():
                                # Preserve folder structure
                                rel_path = os.path.relpath(root, source_folder)
                                dest_dir = os.path.join(dest_folder, rel_path)
                                os.makedirs(dest_dir, exist_ok=True)
                                dest_path = os.path.join(dest_dir, file)
                            else:
                                # Flat structure
                                dest_path = os.path.join(dest_folder, file)
                                
                                # Handle filename conflicts
                                counter = 1
                                base_name, ext = os.path.splitext(file)
                                while os.path.exists(dest_path):
                                    new_name = f"{base_name}_{counter}{ext}"
                                    dest_path = os.path.join(dest_folder, new_name)
                                    counter += 1
                            
                            # Copy or move the file
                            if self.copy_mode.get():
                                shutil.copy2(source_path, dest_path)
                                action = "Copied"
                            else:
                                shutil.move(source_path, dest_path)
                                action = "Moved"
                            
                            extracted_count += 1
                            progress = (extracted_count / total_images) * 100
                            self.update_progress(progress, f"{action}: {file}")
                            
                        except Exception as e:
                            error_count += 1
                            self.update_status(f"Error processing {file}: {str(e)}")
            
            # Final status
            if self.is_processing:
                action_word = "copied" if self.copy_mode.get() else "moved"
                self.update_status(f"\nExtraction completed!")
                self.update_status(f"Successfully {action_word}: {extracted_count} images")
                if error_count > 0:
                    self.update_status(f"Errors encountered: {error_count} files")
                self.update_progress(100, "Extraction finished!")
            else:
                self.update_status("\nExtraction stopped by user.")
                
        except Exception as e:
            self.update_status(f"\nUnexpected error: {str(e)}")
        finally:
            self._reset_ui()

    def _reset_ui(self):
        """Reset UI to initial state"""
        self.is_processing = False
        self.extract_btn.config(state="normal")
        self.stop_btn.config(state="disabled")