import tkinter as tk
from tkinter import ttk, filedialog, messagebox
# from ttkthemes import ThemedTk #removed since we will be using ttkbootstrap
import ttkbootstrap as ttk
from ttkbootstrap.constants import * #add for ttkboot strap
import os
import sys
import threading
import logging
import time
import shutil
from PIL import Image

# Define basic theme colors directly
class AppTheme:
    COLORS = {
        'primary': '#0d6efd',
        'success': '#198754',
        'info': '#0dcaf0',
        'warning': '#ffc107',
        'danger': '#dc3545'
    }
    
    @staticmethod
    def apply_theme(style):
        """Apply basic theme styling"""
        pass

# Specify the absolute path to your modules
MODULE_PATH = '/Users/<USER>/Documents/rebildeing/APP v4'
sys.path.insert(0, MODULE_PATH)

# Import tool modules using the adjusted path
from csv_converter import CSVConverter
from embed_metadata import EmbedMetadata
from image_renamer import ImageRenamer
from png_to_jpeg import PNGtoJPEGConverter
from unzip_images import UnzipImages
from upscaleremover import remove_duplicate_files
from csv_splitter import CSVSplitterFrame
from image_cleaner import ImageCleanerFrame  # Import ImageCleanerFrame
from imagespliter import ImageSplitterFrame  # Import the new ImageSplitterFrame
from image_resizer import ImageResizerFrame  # Import the new ImageResizerFrame
from image_extractor import ImageExtractorFrame  # Import the new ImageExtractorFrame
# Import Clip combiner modules with proper path handling
sys.path.insert(0, os.path.join(MODULE_PATH, 'Clip_png_combiner'))
from auto_variation_finder import ImageVariationFinder
from image_combiner import ImageCombiner


class UpscaleRemoverFrame(ttk.Frame):
    def __init__(self, parent, app):
        super().__init__(parent, padding=10)
        self.app = app
        self.create_widgets()

    def create_widgets(self):
        # Main frame layout with improved styling
        main_frame = ttk.LabelFrame(self, text="Upscale Remover", padding=15, style="info.TLabelframe")
        main_frame.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

        # Warning label
        warning_label = ttk.Label(
            main_frame,
            text="⚠️ WARNING: Duplicates will be removed from Folder One!",
            foreground="red",
            font=("Arial", 12, "bold")
        )
        warning_label.pack(pady=(0, 20))

        # Folder selection inputs
        folder_frame = ttk.Frame(main_frame)
        folder_frame.pack(pady=15, fill=tk.X)

        # Folder One Selection
        folder_one_label = ttk.Label(folder_frame, text="Folder One (Source):", font=("Helvetica", 12, "bold"))
        folder_one_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=8)

        self.folder_one_entry = ttk.Entry(folder_frame, font=("Helvetica", 12))
        self.folder_one_entry.grid(row=0, column=1, padx=5, pady=8, sticky=tk.EW)

        folder_one_browse = ttk.Button(folder_frame, text="Browse", command=self.browse_folder_one, 
                                     style="success.TButton")
        folder_one_browse.grid(row=0, column=2, padx=5, pady=8)

        # Folder Two Selection
        folder_two_label = ttk.Label(folder_frame, text="Folder Two (Reference):", font=("Helvetica", 12, "bold"))
        folder_two_label.grid(row=1, column=0, sticky=tk.W, padx=5, pady=8)

        self.folder_two_entry = ttk.Entry(folder_frame, font=("Helvetica", 12))
        self.folder_two_entry.grid(row=1, column=1, padx=5, pady=8, sticky=tk.EW)

        folder_two_browse = ttk.Button(folder_frame, text="Browse", command=self.browse_folder_two, 
                                     style="success.TButton")
        folder_two_browse.grid(row=1, column=2, padx=5, pady=8)

        # Configure columns for responsive resizing
        folder_frame.columnconfigure(1, weight=1)

        # Description
        description = """
        How it works:
        1. Select two folders using the Browse buttons
        2. The app will find images with matching names (ignoring extensions)
        3. Matching images will be REMOVED from Folder One
        4. Folder Two remains unchanged
        """
        desc_label = ttk.Label(main_frame, text=description, justify=tk.LEFT, 
                             font=("Helvetica", 10))
        desc_label.pack(pady=20, anchor=tk.W)

        # Process button
        process_btn = ttk.Button(
            main_frame,
            text="Remove Duplicates",
            command=self.start_remove_duplicates,
            style="primary-outline.TButton",
            width=20
        )
        process_btn.pack(pady=20)

        # Status frame
        status_frame = ttk.Frame(main_frame, style="light.TFrame")
        status_frame.pack(fill=tk.X, padx=15, pady=10)
        
        self.status_label = ttk.Label(status_frame, text="Ready", font=("Helvetica", 10))
        self.status_label.pack(pady=5)

    def browse_folder_one(self):
        folder_selected = filedialog.askdirectory()
        if folder_selected:
            self.folder_one_entry.delete(0, tk.END)
            self.folder_one_entry.insert(0, folder_selected)
            self.status_label.config(text=f"Selected source folder: {os.path.basename(folder_selected)}")

    def browse_folder_two(self):
        folder_selected = filedialog.askdirectory()
        if folder_selected:
            self.folder_two_entry.delete(0, tk.END)
            self.folder_two_entry.insert(0, folder_selected)
            self.status_label.config(text=f"Selected destination folder: {os.path.basename(folder_selected)}")

    def start_remove_duplicates(self):
        folder_one = self.folder_one_entry.get()
        folder_two = self.folder_two_entry.get()

        if not folder_one or not folder_two:
            messagebox.showerror("Error", "Please select both folders.")
            return

        if messagebox.askyesno("Confirm", 
                              "This will permanently delete duplicate images from Folder One.\n"
                              "Are you sure you want to continue?"):
            self.status_label.config(text="Processing... Please wait")
            threading.Thread(target=self.run_remove_duplicates, args=(folder_one, folder_two), daemon=True).start()

    def run_remove_duplicates(self, folder_one, folder_two):
        try:
            removed_count, error_messages = remove_duplicate_files(folder_one, folder_two)
            
            # Schedule UI updates in the main thread
            self.app.root.after(0, self._update_ui_after_removal, removed_count, error_messages)
        except Exception as e:
            # Schedule error handling in the main thread
            self.app.root.after(0, self._handle_removal_error, str(e))
    
    def _update_ui_after_removal(self, removed_count, error_messages):
        """Handle UI updates after removal process (runs in main thread)"""
        if error_messages:
            for error in error_messages:
                self.app.update_status(f"Error: {error}")
            messagebox.showerror("Error", f"Encountered Error: {error_messages}")
            self.status_label.config(text="Process failed - see error details")
        else:
            self.app.update_status(f"Removed {removed_count} duplicate files.")
            messagebox.showinfo("Success", f"Removed {removed_count} duplicate files.")
            self.status_label.config(text=f"Successfully removed {removed_count} files")
    
    def _handle_removal_error(self, error_message):
        """Handle unexpected errors during removal process (runs in main thread)"""
        self.app.update_status(f"Error: {error_message}")
        messagebox.showerror("Error", f"An unexpected error occurred: {error_message}")
        self.status_label.config(text="Process failed - unexpected error")


class ImageMoverFrame(ttk.Frame):
    def __init__(self, parent, app):
        super().__init__(parent, padding=10)
        self.app = app
        self.source_dir = ""
        self.destination_dir = ""
        self.running = False
        self.image_extensions = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.tiff', '.webp']
        self.interval = 600  # 10 minutes in seconds
        self.create_widgets()

    def create_widgets(self):
        # Main frame layout with improved styling
        main_frame = ttk.LabelFrame(self, text="Image Mover (Keep Recent)", padding=15, style="info.TLabelframe")
        main_frame.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)

        # Source Folder Selection
        source_frame = ttk.Frame(main_frame)
        source_frame.pack(pady=5, fill=tk.X)

        ttk.Label(source_frame, text="Source Folder:", font=("Helvetica", 12, "bold")).grid(row=0, column=0, sticky=tk.W, padx=5)
        self.source_entry = ttk.Entry(source_frame, width=50)
        self.source_entry.grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(source_frame, text="Browse", command=self.browse_source, style="success.TButton").grid(row=0, column=2, padx=5)

        # Destination Folder Selection
        dest_frame = ttk.Frame(main_frame)
        dest_frame.pack(pady=5, fill=tk.X)

        ttk.Label(dest_frame, text="Destination Folder:", font=("Helvetica", 12, "bold")).grid(row=0, column=0, sticky=tk.W, padx=5)
        self.dest_entry = ttk.Entry(dest_frame, width=50)
        self.dest_entry.grid(row=0, column=1, padx=5, pady=5)
        ttk.Button(dest_frame, text="Browse", command=self.browse_dest, style="success.TButton").grid(row=0, column=2, padx=5)

        # Interval Setting
        interval_frame = ttk.Frame(main_frame)
        interval_frame.pack(pady=5, fill=tk.X)

        ttk.Label(interval_frame, text="Check Interval (minutes):", font=("Helvetica", 12, "bold")).grid(row=0, column=0, sticky=tk.W, padx=5)
        self.interval_entry = ttk.Entry(interval_frame, width=10)
        self.interval_entry.insert(0, str(self.interval // 60))
        self.interval_entry.grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)

        # Start/Stop Button
        self.start_button = ttk.Button(main_frame, text="Start Processing", command=self.toggle_processing, style="primary.TButton", width=20)
        self.start_button.pack(pady=10)

        # Status Label
        self.status_label = ttk.Label(main_frame, text="Waiting...", font=("Helvetica", 10))
        self.status_label.pack(pady=5)

    def browse_source(self):
        folder = filedialog.askdirectory()
        if folder:
            self.source_dir = folder
            self.source_entry.delete(0, tk.END)
            self.source_entry.insert(0, folder)

    def browse_dest(self):
        folder = filedialog.askdirectory()
        if folder:
            self.destination_dir = folder
            self.dest_entry.delete(0, tk.END)
            self.dest_entry.insert(0, folder)

    def toggle_processing(self):
        if not self.running:
            try:
                interval_minutes = int(self.interval_entry.get())
                if interval_minutes <= 0:
                    messagebox.showerror("Error", "Interval must be greater than 0 minutes.")
                    return
                self.interval = interval_minutes * 60
            except ValueError:
                messagebox.showerror("Error", "Invalid interval. Please enter a valid number of minutes.")
                return

            if not self.source_dir or not self.destination_dir:
                messagebox.showerror("Error", "Please select both source and destination folders.")
                return
            if not os.path.isdir(self.source_dir):
                messagebox.showerror("Error", "Source folder not valid.")
                return
            if not os.path.isdir(self.destination_dir):
                messagebox.showerror("Error", "Destination folder not valid.")
                return
            self.start_processing()
        else:
            self.stop_processing()

    def start_processing(self):
        self.running = True
        self.start_button.config(text="Stop Processing")
        self.status_label.config(text=f"Processing Scheduled (Every {self.interval//60} minutes)...")
        self.thread = threading.Thread(target=self.scheduled_move)
        self.thread.daemon = True
        self.thread.start()
        logging.info(f"Scheduled processing started with {self.interval//60} minute interval.")

    def stop_processing(self):
        self.running = False
        self.start_button.config(text="Start Processing")
        self.status_label.config(text="Stopped")
        logging.info("Scheduled processing stopped.")

    def scheduled_move(self):
        while self.running:
            logging.info("Scheduled processing tick.")
            self.process_images()
            time.sleep(self.interval)

    def process_images(self):
        if not self.source_dir or not self.destination_dir:
            return

        files_to_move = []
        error_files = []
        
        for filename in os.listdir(self.source_dir):
            file_path = os.path.join(self.source_dir, filename)
            if os.path.isfile(file_path) and any(filename.lower().endswith(ext) for ext in self.image_extensions):
                try:
                    with PIL.Image.open(file_path) as img:
                        files_to_move.append((file_path, os.path.getmtime(file_path)))
                except Exception as e:
                    error_files.append(f"File {filename} error: {e}")
                    logging.error(f"Error checking {filename}: {e}")

        if not files_to_move:
            if error_files:
                # Schedule UI update in the main thread
                self.app.root.after(0, lambda: self._update_status(
                    f"Found errors: {len(error_files)}. No image found to be moved.", 
                    "danger"
                ))
                logging.info(f"No valid images to move after detection. Number of images which could not be checked : {len(error_files)}")
            else:
                logging.info(f"No valid images to move")
            return

        # Sort files by modification time (newest first)
        files_to_move.sort(key=lambda item: item[1], reverse=True)
        
        # Keep the 10 most recent files, move the rest
        files_to_keep = files_to_move[:10]
        files_to_move = files_to_move[10:]
        
        moved_count = 0
        move_errors = []
        
        for file_path, _ in files_to_move:
            filename = os.path.basename(file_path)
            try:
                new_path = os.path.join(self.destination_dir, filename)
                shutil.move(file_path, new_path)
                moved_count += 1
            except Exception as e:
                move_errors.append(f"Failed to move {filename}: {e}")
                logging.error(f"Error moving {filename}: {e}")

        # Schedule UI update in the main thread
        self.app.root.after(0, lambda: self._update_process_results(
            moved_count, len(files_to_keep), move_errors
        ))
        logging.info(f"Moved {moved_count} image(s), kept {len(files_to_keep)} recent images. {len(move_errors)} move errors in this interval.")

    def _update_status(self, message, status_type):
        """Update status label with message and status type (runs in main thread)"""
        self.status_label.config(text=message)
        
    def _update_process_results(self, moved_count, kept_count, move_errors):
        """Update UI with process results (runs in main thread)"""
        if moved_count == 0:
            if move_errors:
                self.status_label.config(text=f"No images moved, encountered errors: {len(move_errors)}")
            else:
                self.status_label.config(text=f"No images to move. Keeping {kept_count} recent images.")
        else:
            self.status_label.config(
                text=f"Moved {moved_count} image(s), kept {kept_count} recent images. {len(move_errors)} errors occurred."
            )


class ClipCombinerFrame(ttk.Frame):
    def __init__(self, parent, app):
        super().__init__(parent, padding=10)
        self.app = app
        
        # Initialize variables first
        self.input_dir = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.layout_mode = tk.StringVar(value="grid")
        self.batch_size = tk.IntVar(value=4)
        self.output_quality = tk.IntVar(value=95)
        self.spacing = tk.IntVar(value=10)
        self.background_color = tk.StringVar(value="white")
        
        # CLIP variables
        self.clip_threshold = tk.DoubleVar(value=0.85)
        self.clip_model = tk.StringVar(value="ViT-B/32")
        self.device = tk.StringVar(value="auto")
        self.aspect_ratio_tolerance = tk.DoubleVar(value=0.05)
        
        # Additional options
        self.generate_master_file = tk.BooleanVar(value=False)
        
        # Progress tracking
        self.is_processing = False
        self.variation_finder = None  # Lazy loading
        
        # Create widgets after variables are initialized
        self.create_widgets()

    def create_widgets(self):
        # Main frame layout with improved styling
        main_frame = ttk.LabelFrame(self, text="Clip Image Combiner", padding=15, style="info.TLabelframe")
        main_frame.pack(padx=10, pady=10, fill=tk.BOTH, expand=True)
        
        # Configure grid weights
        main_frame.columnconfigure(1, weight=1)
        
        # Input Directory Section
        ttk.Label(main_frame, text="Input Directory:", font=('Arial', 10, 'bold')).grid(
            row=0, column=0, sticky=tk.W, pady=(0, 5))
        
        input_frame = ttk.Frame(main_frame)
        input_frame.grid(row=1, column=0, columnspan=3, sticky="we", pady=(0, 15))
        input_frame.columnconfigure(0, weight=1)
        
        ttk.Entry(input_frame, textvariable=self.input_dir, width=60).grid(
            row=0, column=0, sticky="we", padx=(0, 10))
        ttk.Button(input_frame, text="Browse", 
                  command=self.browse_input_dir).grid(row=0, column=1)
        
        # Output Directory Section
        ttk.Label(main_frame, text="Output Directory:", font=('Arial', 10, 'bold')).grid(
            row=2, column=0, sticky=tk.W, pady=(0, 5))
        
        output_frame = ttk.Frame(main_frame)
        output_frame.grid(row=3, column=0, columnspan=3, sticky="we", pady=(0, 15))
        output_frame.columnconfigure(0, weight=1)
        
        ttk.Entry(output_frame, textvariable=self.output_dir, width=60).grid(
            row=0, column=0, sticky="we", padx=(0, 10))
        ttk.Button(output_frame, text="Browse", 
                  command=self.browse_output_dir).grid(row=0, column=1)
        
        # Settings Section
        settings_frame = ttk.LabelFrame(main_frame, text="Settings", padding="15")
        settings_frame.grid(row=4, column=0, columnspan=3, sticky="we", pady=(0, 15))
        settings_frame.columnconfigure(1, weight=1)
        
        # Layout Mode
        ttk.Label(settings_frame, text="Layout Mode:").grid(row=0, column=0, sticky=tk.W, pady=5)
        layout_combo = ttk.Combobox(settings_frame, textvariable=self.layout_mode, 
                                   values=["grid", "horizontal", "vertical"], state="readonly")
        layout_combo.grid(row=0, column=1, sticky="we", padx=(10, 0), pady=5)
        
        # Max Batch Size
        ttk.Label(settings_frame, text="Max Batch Size:").grid(row=1, column=0, sticky=tk.W, pady=5)
        batch_spin = ttk.Spinbox(settings_frame, from_=2, to=16, textvariable=self.batch_size, width=10)
        batch_spin.grid(row=1, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # CLIP Model Selection
        ttk.Label(settings_frame, text="CLIP Model:").grid(row=2, column=0, sticky=tk.W, pady=5)
        model_combo = ttk.Combobox(settings_frame, textvariable=self.clip_model, 
                                  values=["ViT-B/32", "ViT-L/14", "RN50", "RN101"], 
                                  state="readonly")
        model_combo.grid(row=2, column=1, sticky="we", padx=(10, 0), pady=5)
        
        # CLIP Threshold
        ttk.Label(settings_frame, text="CLIP Similarity Threshold:").grid(row=3, column=0, sticky=tk.W, pady=5)
        threshold_frame = ttk.Frame(settings_frame)
        threshold_frame.grid(row=3, column=1, sticky="we", padx=(10, 0), pady=5)
        threshold_scale = ttk.Scale(threshold_frame, from_=0.5, to=0.95, variable=self.clip_threshold, 
                                   orient=tk.HORIZONTAL)
        threshold_scale.grid(row=0, column=0, sticky="we")
        threshold_frame.columnconfigure(0, weight=1)
        self.clip_threshold_label = ttk.Label(threshold_frame, text="0.85")
        self.clip_threshold_label.grid(row=0, column=1, padx=(10, 0))
        
        # Update threshold label when scale changes
        self.clip_threshold.trace_add('write', self.update_clip_threshold_label)
        
        # Spacing
        ttk.Label(settings_frame, text="Image Spacing (px):").grid(row=4, column=0, sticky=tk.W, pady=5)
        spacing_spin = ttk.Spinbox(settings_frame, from_=0, to=100, textvariable=self.spacing, width=10)
        spacing_spin.grid(row=4, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Background Color
        ttk.Label(settings_frame, text="Background Color:").grid(row=5, column=0, sticky=tk.W, pady=5)
        color_combo = ttk.Combobox(settings_frame, textvariable=self.background_color,
                                  values=["white", "black", "gray", "transparent"], state="readonly")
        color_combo.grid(row=5, column=1, sticky="we", padx=(10, 0), pady=5)
        
        # Quality
        ttk.Label(settings_frame, text="Output Quality (%):").grid(row=6, column=0, sticky=tk.W, pady=5)
        quality_spin = ttk.Spinbox(settings_frame, from_=70, to=100, textvariable=self.output_quality, width=10)
        quality_spin.grid(row=6, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Generate Master File
        ttk.Label(settings_frame, text="Generate Master File:").grid(row=7, column=0, sticky=tk.W, pady=5)
        master_check = ttk.Checkbutton(settings_frame, variable=self.generate_master_file)
        master_check.grid(row=7, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Progress Section
        progress_frame = ttk.Frame(main_frame)
        progress_frame.grid(row=5, column=0, columnspan=3, sticky="we", pady=(0, 15))
        progress_frame.columnconfigure(0, weight=1)
        
        self.progress_var = tk.StringVar(value="Ready to process images...")
        self.progress_label = ttk.Label(progress_frame, textvariable=self.progress_var)
        self.progress_label.grid(row=0, column=0, sticky=tk.W)
        
        self.progress_bar = ttk.Progressbar(progress_frame, mode='indeterminate')
        self.progress_bar.grid(row=1, column=0, sticky="we", pady=(5, 0))
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=6, column=0, columnspan=3, pady=(0, 10))
        
        self.load_model_button = ttk.Button(button_frame, text="Load Model", 
                                           command=self.load_clip_model, style='info.TButton')
        self.load_model_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.process_button = ttk.Button(button_frame, text="Process Images", 
                                        command=self.start_processing, style='success.TButton')
        self.process_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="Open Output Folder", 
                  command=self.open_output_folder).pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="Reset Settings", 
                  command=self.reset_settings).pack(side=tk.LEFT)

    def browse_input_dir(self):
        directory = filedialog.askdirectory(title="Select Input Directory")
        if directory:
            self.input_dir.set(directory)
            self.app.update_status(f"Input directory selected: {directory}")
    
    def browse_output_dir(self):
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_dir.set(directory)
            self.app.update_status(f"Output directory selected: {directory}")
    
    def update_clip_threshold_label(self, *args):
        """Update the CLIP threshold label"""
        self.clip_threshold_label.config(text=f"{self.clip_threshold.get():.2f}")
    
    def reset_settings(self):
        """Reset all settings to default values"""
        self.layout_mode.set("grid")
        self.batch_size.set(4)
        self.output_quality.set(95)
        self.spacing.set(10)
        self.background_color.set("white")
        self.clip_threshold.set(0.85)
        self.clip_model.set("ViT-B/32")
        self.device.set("auto")
        self.aspect_ratio_tolerance.set(0.05)
        self.generate_master_file.set(False)
        self.app.update_status("Settings reset to defaults")
    
    def open_output_folder(self):
        if self.output_dir.get() and os.path.exists(self.output_dir.get()):
            os.system(f'open "{self.output_dir.get()}"')
        else:
            messagebox.showwarning("Warning", "Output directory not found or not set")
    
    def start_processing(self):
        if self.is_processing:
            return
        
        # Validate inputs
        if not self.input_dir.get():
            messagebox.showerror("Error", "Please select an input directory")
            return
        
        if not self.output_dir.get():
            messagebox.showerror("Error", "Please select an output directory")
            return
        
        if not os.path.exists(self.input_dir.get()):
            messagebox.showerror("Error", "Input directory does not exist")
            return
        
        # Create output directory if it doesn't exist
        os.makedirs(self.output_dir.get(), exist_ok=True)
        
        # Start processing in a separate thread
        self.is_processing = True
        self.process_button.config(state='disabled')
        self.progress_bar.start()
        
        thread = threading.Thread(target=self.process_images)
        thread.daemon = True
        thread.start()
    
    def load_clip_model(self):
        """Load CLIP model lazily in a separate thread"""
        if self.variation_finder is None:
            # Disable button and start progress
            self.load_model_button.config(state='disabled')
            self.progress_bar.start()
            
            # Start loading in a separate thread
            thread = threading.Thread(target=self._load_model_thread)
            thread.daemon = True
            thread.start()
        else:
            messagebox.showinfo("Info", "CLIP model is already loaded!")
    
    def _load_model_thread(self):
        """Load CLIP model in background thread"""
        try:
            self.progress_var.set("Loading CLIP model...")
            self.app.update_status("Loading CLIP model...")
            
            self.variation_finder = ImageVariationFinder(
                clip_threshold=self.clip_threshold.get(),
                clip_model_name=self.clip_model.get(),
                device=self.device.get(),
                aspect_ratio_tolerance=self.aspect_ratio_tolerance.get()
            )
            
            # Update UI in main thread
            self.app.root.after(0, self._model_loaded_success)
            
        except Exception as e:
            error_msg = f"Error loading CLIP model: {str(e)}"
            # Update UI in main thread
            self.app.root.after(0, lambda: self._model_loaded_error(error_msg))
    
    def _model_loaded_success(self):
        """Handle successful model loading (runs in main thread)"""
        self.progress_var.set("CLIP model loaded successfully")
        self.app.update_status("CLIP model loaded")
        self.progress_bar.stop()
        self.load_model_button.config(state='normal')
        messagebox.showinfo("Success", "CLIP model loaded successfully!")
    
    def _model_loaded_error(self, error_msg):
        """Handle model loading error (runs in main thread)"""
        self.progress_var.set("Model loading failed")
        self.progress_bar.stop()
        self.load_model_button.config(state='normal')
        messagebox.showerror("Error", error_msg)
        self.variation_finder = None  # Reset on error
    
    def process_images(self):
        try:
            self.progress_var.set("Initializing image processing...")
            self.app.update_status("Starting image processing...")
            
            # Load CLIP model if not already loaded
            if self.variation_finder is None:
                self.load_clip_model()
            
            # Use the loaded variation finder
            finder = self.variation_finder
            
            # Process the directory
            self.progress_var.set("Scanning for similar images...")
            batches = finder.process_directory(
                input_dir=self.input_dir.get(),
                output_dir=self.output_dir.get(),
                batch_size=self.batch_size.get(),
                target_size=None,  # Use dynamic sizing
                layout_mode=self.layout_mode.get(),
                spacing=self.spacing.get(),
                background_color=self.background_color.get(),
                quality=self.output_quality.get(),
                generate_master_file=self.generate_master_file.get()
            )
            
            self.progress_var.set(f"Processing complete!")
            
            # Count results
            total_batches = len(batches) if batches else 0
            single_image_batches = sum(1 for batch in batches if batch and len(batch) == 1) if batches else 0
            multi_image_batches = total_batches - single_image_batches
            
            # Show completion message
            self.app.root.after(0, lambda: messagebox.showinfo(
                "Success", 
                f"Processing complete!\n\n"
                f"Generated {total_batches} batches:\n"
                f"- {single_image_batches} single images preserved as-is\n"
                f"- {multi_image_batches} similar image groups combined\n\n"
                f"Check the output directory for results."
            ))
            
        except Exception as e:
            error_msg = f"Error during processing: {str(e)}"
            self.progress_var.set("Processing failed")
            self.app.root.after(0, lambda: messagebox.showerror("Error", error_msg))
        
        finally:
            # Reset UI state
            self.is_processing = False
            self.app.root.after(0, lambda: [
                self.progress_bar.stop(),
                self.process_button.config(state='normal'),
                self.progress_var.set("Ready to process images...")
            ])


class ImageToolboxApp:
    def __init__(self, root):
        self.root = root
        self.root.title("Image Toolbox Professional")
        
        # Set window size and minimum size
        self.root.geometry("1280x900")
        self.root.minsize(1000, 700)
        
        # Configure the theme
        self.style = ttk.Style()
        AppTheme.apply_theme(self.style)
        
        # Current active tool frame
        self.active_tool = None
        self.tool_frames = {}
        
        # Create the main application layout
        self.setup_layout()
        
        # Create tools
        self.create_tools()
    
    def setup_layout(self):
        """Create the main application layout structure"""
        # Main container with padding and card styling
        self.main_container = ttk.Frame(self.root, padding="10", style='Card.TFrame')
        self.main_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create header with app title and controls
        self.create_header()
        
        # Create main content area with sidebar and tool frames
        self.content_area = ttk.Frame(self.main_container)
        self.content_area.pack(fill=tk.BOTH, expand=True, pady=(10, 0))
        
        # Create sidebar for tool selection
        self.create_sidebar()
        
        # Create tool container area
        self.tool_container = ttk.Frame(self.content_area)
        self.tool_container.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create status bar
        self.create_status_bar()
    
    def create_header(self):
        """Create a simple header with app title"""
        # Simple header with just title
        self.header_frame = ttk.Frame(self.main_container)
        self.header_frame.pack(fill=tk.X, pady=(0, 10))
        
        # App title
        app_title = ttk.Label(
            self.header_frame,
            text="Image Toolbox",
            font=("Arial", 16, "bold")
        )
        app_title.pack(pady=10)
    
    def create_sidebar(self):
        """Create a simple sidebar with tool buttons"""
        # Simple sidebar frame
        self.sidebar = ttk.Frame(self.content_area, width=200)
        self.sidebar.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        self.sidebar.pack_propagate(False)
        
        # Create frame for sidebar content
        self.sidebar_frame = ttk.Frame(self.sidebar)
        self.sidebar_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    def create_status_bar(self):
        """Create a simple status bar"""
        # Simple status bar
        self.status_frame = ttk.Frame(self.main_container)
        self.status_frame.pack(fill=tk.X, pady=(10, 0))
        
        # Status label
        self.status_label = ttk.Label(
            self.status_frame,
            text="Ready",
            font=("Arial", 10)
        )
        self.status_label.pack(side=tk.LEFT, padx=10, pady=5)

    def create_tools(self):
        """Create tool frames and sidebar buttons"""
        # Define tool list - simplified
        tools = [
            ("CSV Converter", CSVConverter, False),
            ("CSV Splitter", CSVSplitterFrame, True),
            ("Embed Metadata", EmbedMetadata, False),
            ("Image Renamer", ImageRenamer, False),
            ("PNG to JPEG", PNGtoJPEGConverter, False),
            ("Unzip Images", UnzipImages, False),
            ("Image Mover", ImageMoverFrame, True),
            ("Image Cleaner", ImageCleanerFrame, True),
            ("Image Splitter", ImageSplitterFrame, True),
            ("Image Resizer", ImageResizerFrame, True),
            ("Image Extractor", ImageExtractorFrame, True),
            ("Clip Combiner", ClipCombinerFrame, True),
            ("Upscale Remover", UpscaleRemoverFrame, True)
        ]
        
        # Create tool buttons
        for tool_name, tool_class, needs_app in tools:
            self.create_tool_button(self.sidebar_frame, tool_name, tool_class, needs_app)
        
        # Show the first tool by default
        if self.tool_frames:
            first_tool = next(iter(self.tool_frames.values()))
            self.show_tool(first_tool["name"])
    
    def create_tool_button(self, parent, tool_name, tool_class, needs_app=False):
        """Create a simple button for a tool in the sidebar"""
        # Create the tool frame based on class type and needs_app flag
        if needs_app:
            # Classes that need the app reference
            tool_instance = tool_class(self.tool_container, self)
        else:
            # Classes that only need the parent container
            tool_instance = tool_class(self.tool_container)
        
        # Determine if the tool instance has a frame attribute
        if hasattr(tool_instance, 'frame'):
            tool_frame = tool_instance.frame
        else:
            # The tool instance is the frame itself
            tool_frame = tool_instance
        
        # Hide the tool frame initially
        tool_frame.pack_forget()
        
        # Store the tool frame
        self.tool_frames[tool_name] = {
            "frame": tool_frame,
            "instance": tool_instance,
            "name": tool_name
        }
        
        # Create a simple button for the tool
        tool_button = ttk.Button(
            parent,
            text=tool_name,
            command=lambda tn=tool_name: self.show_tool(tn)
        )
        tool_button.pack(fill=tk.X, padx=5, pady=2)
        
        # Store the button reference
        self.tool_frames[tool_name]["button"] = tool_button
    
    def show_tool(self, tool_name):
        """Show the selected tool and hide others"""
        # Hide current active tool if any
        if self.active_tool:
            self.tool_frames[self.active_tool]["frame"].pack_forget()
        
        # Show selected tool
        tool_frame = self.tool_frames[tool_name]["frame"]
        tool_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Update active tool
        self.active_tool = tool_name
        
        # Update status
        self.update_status(f"Selected: {tool_name}")
    
    def update_status(self, message):
        """Update the status bar with a message"""
        self.status_label.config(text=message)

    def show_about(self):
        messagebox.showinfo("About Image Toolbox", "Simple Image Toolbox Application")

    def quit_app(self, event=None):
        self.root.destroy()

    def run(self):
        self.root.mainloop()


if __name__ == "__main__":
    root = ttk.Window()
    app = ImageToolboxApp(root)
    app.run()
