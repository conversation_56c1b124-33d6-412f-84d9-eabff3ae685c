-- Image Toolbox Launcher <PERSON>Script
-- This script can be used with Automator to launch the Image Toolbox application

-- Get the path to the project directory
set projectPath to (path to me as text)
set AppleScript's text item delimiters to ":"
set pathItems to text items of projectPath
set AppleScript's text item delimiters to ""

-- Remove the script name from the path to get the directory
set projectDir to ""
repeat with i from 1 to (count of pathItems) - 1
	if i > 1 then set projectDir to projectDir & "/"
	set projectDir to projectDir & (item i of pathItems)
end repeat

-- Convert to POSIX path
set projectDirPOSIX to POSIX path of (projectDir & ":" as alias)

-- Set up the shell command to run the launcher script
set shellCommand to "cd '" & projectDirPOSIX & "' && ./launch_app.sh"

-- Execute the command in Terminal (visible for debugging)
tell application "Terminal"
	activate
	do script shellCommand
end tell
