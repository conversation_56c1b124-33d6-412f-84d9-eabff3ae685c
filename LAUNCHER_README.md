# Image Toolbox Launcher Scripts

This directory contains several launcher scripts that allow you to run the Image Toolbox application with the correct virtual environment from various contexts, including macOS Automator.

## Available Launchers

### 1. `launcher.py` (Recommended)
**Python-based launcher with comprehensive error handling and logging**

- **Usage**: `python3 launcher.py` or `./launcher.py`
- **Best for**: Direct execution, debugging, and most reliable operation
- **Features**:
  - Comprehensive error checking
  - Detailed logging to `launcher.log`
  - Cross-platform compatibility
  - Automatic virtual environment detection
  - Environment variable setup

### 2. `launch_app.sh`
**Bash script launcher**

- **Usage**: `./launch_app.sh`
- **Best for**: Terminal users familiar with shell scripts
- **Features**:
  - Lightweight shell script
  - Basic error checking
  - Logging to `launcher.log`
  - Virtual environment activation

### 3. `launch_app.applescript`
**AppleScript for macOS integration**

- **Usage**: Open with Script Editor or use in Automator
- **Best for**: macOS Automator workflows
- **Features**:
  - Native macOS integration
  - Opens Terminal window for visibility
  - Can be compiled as an application

## Setting Up with macOS Automator

### Method 1: Using the Python Launcher (Recommended)

1. Open **Automator** on your Mac
2. Create a new **Application**
3. Add a **Run Shell Script** action
4. Set the shell to `/bin/bash`
5. Enter this command:
   ```bash
   cd "/Users/<USER>/Documents/rebildeing/APP v4"
   python3 launcher.py
   ```
6. Save the Automator application (e.g., "Image Toolbox Launcher.app")

### Method 2: Using the Bash Script

1. Open **Automator** on your Mac
2. Create a new **Application**
3. Add a **Run Shell Script** action
4. Set the shell to `/bin/bash`
5. Enter this command:
   ```bash
   cd "/Users/<USER>/Documents/rebildeing/APP v4"
   ./launch_app.sh
   ```
6. Save the Automator application

### Method 3: Using AppleScript

1. Open **Automator** on your Mac
2. Create a new **Application**
3. Add a **Run AppleScript** action
4. Copy the contents of `launch_app.applescript` into the script area
5. Save the Automator application

## Direct Usage

### From Terminal
```bash
# Navigate to the project directory
cd "/Users/<USER>/Documents/rebildeing/APP v4"

# Run using Python launcher (recommended)
python3 launcher.py

# Or run using bash script
./launch_app.sh
```

### From Finder
- Double-click on `launcher.py` (if Python is associated with .py files)
- Or create an Automator application as described above

## Troubleshooting

### Check the Log File
Both launchers create a `launcher.log` file in the project directory. Check this file for detailed error messages:

```bash
tail -f launcher.log
```

### Common Issues

1. **Virtual Environment Not Found**
   - Ensure the `venv` folder exists in the project directory
   - Recreate the virtual environment if necessary:
     ```bash
     python3 -m venv venv
     source venv/bin/activate
     pip install -r requirements.txt
     ```

2. **Permission Denied**
   - Make sure the scripts are executable:
     ```bash
     chmod +x launcher.py
     chmod +x launch_app.sh
     ```

3. **Python Not Found**
   - Ensure Python 3 is installed and accessible
   - Check with: `python3 --version`

4. **Dependencies Missing**
   - Activate the virtual environment and install requirements:
     ```bash
     source venv/bin/activate
     pip install -r requirements.txt
     ```

## File Structure
```
APP v4/
├── launcher.py              # Python launcher (recommended)
├── launch_app.sh           # Bash launcher
├── launch_app.applescript  # AppleScript launcher
├── launcher.log            # Log file (created when running)
├── main_app.py            # Main application
├── venv/                  # Virtual environment
├── requirements.txt       # Python dependencies
└── ...                    # Other project files
```

## Notes

- All launchers automatically activate the virtual environment
- The Python launcher (`launcher.py`) is recommended for most use cases
- Logs are written to `launcher.log` for debugging
- The launchers work from any location as they auto-detect the project directory
- For Automator applications, you can customize the icon by right-clicking the saved app and selecting "Get Info"
