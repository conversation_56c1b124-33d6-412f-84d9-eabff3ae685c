#!/bin/bash

# Image Toolbox Launcher Script
# This script activates the virtual environment and runs the main application
# Designed to work with macOS Automator

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Set the project directory (same as script directory)
PROJECT_DIR="$SCRIPT_DIR"

# Path to the virtual environment
VENV_PATH="$PROJECT_DIR/venv"

# Path to the main application
MAIN_APP="$PROJECT_DIR/main_app.py"

# Log file for debugging
LOG_FILE="$PROJECT_DIR/launcher.log"

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" >> "$LOG_FILE"
}

# Start logging
log_message "=== Starting Image Toolbox Application ==="
log_message "Script directory: $SCRIPT_DIR"
log_message "Project directory: $PROJECT_DIR"
log_message "Virtual environment: $VENV_PATH"
log_message "Main application: $MAIN_APP"

# Check if virtual environment exists
if [ ! -d "$VENV_PATH" ]; then
    log_message "ERROR: Virtual environment not found at $VENV_PATH"
    osascript -e 'display alert "Error" message "Virtual environment not found. Please ensure the venv folder exists in the project directory."'
    exit 1
fi

# Check if main application exists
if [ ! -f "$MAIN_APP" ]; then
    log_message "ERROR: Main application not found at $MAIN_APP"
    osascript -e 'display alert "Error" message "Main application file (main_app.py) not found."'
    exit 1
fi

# Change to project directory
cd "$PROJECT_DIR"
log_message "Changed to project directory: $(pwd)"

# Activate virtual environment
log_message "Activating virtual environment..."
source "$VENV_PATH/bin/activate"

# Check if activation was successful
if [ "$VIRTUAL_ENV" != "$VENV_PATH" ]; then
    log_message "ERROR: Failed to activate virtual environment"
    osascript -e 'display alert "Error" message "Failed to activate virtual environment."'
    exit 1
fi

log_message "Virtual environment activated successfully"
log_message "Python path: $(which python)"
log_message "Python version: $(python --version)"

# Set PYTHONPATH to include the project directory
export PYTHONPATH="$PROJECT_DIR:$PYTHONPATH"
log_message "PYTHONPATH set to: $PYTHONPATH"

# Run the application
log_message "Starting main application..."
python "$MAIN_APP" 2>&1 | while IFS= read -r line; do
    log_message "APP: $line"
done

# Log completion
log_message "Application finished"
log_message "=== End of session ==="
